#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffe1c3f78c5, pid=5584, tid=8940
#
# JRE version: OpenJDK Runtime Environment (21.0.5) (build 21.0.5+-13047016-b750.29)
# Java VM: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# V  [jvm.dll+0x3178c5]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://youtrack.jetbrains.com/issues/JBR
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1

Host: Intel(R) Core(TM) i5-7400 CPU @ 3.00GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.3031)
Time: Fri May 16 01:38:14 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.3031) elapsed time: 2931.514497 seconds (0d 0h 48m 51s)

---------------  T H R E A D  ---------------

Current thread (0x000001edc3a95370):  WorkerThread "G1 Conc#0"      [id=8940, stack(0x0000006761400000,0x0000006761500000) (1024K)]

Stack: [0x0000006761400000,0x0000006761500000],  sp=0x00000067614ff780,  free space=1021k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x3178c5]
V  [jvm.dll+0x31775f]
V  [jvm.dll+0x31509d]
V  [jvm.dll+0x314ce2]
V  [jvm.dll+0x334810]
V  [jvm.dll+0x331061]
V  [jvm.dll+0x332bec]
V  [jvm.dll+0x331933]
V  [jvm.dll+0x335296]
V  [jvm.dll+0x88bc74]
V  [jvm.dll+0x806368]
V  [jvm.dll+0x6ce3fd]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17614]
C  [ntdll.dll+0x526f1]


siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0x000001f5c3a52830


Registers:
RAX=0x000001edc3a56800, RBX=0x000001edc3a97280, RCX=0x0000000000000014, RDX=0x0000000000000000
RSP=0x00000067614ff780, RBP=0x0000000000000000, RSI=0x000001edc3a82ef0, RDI=0x00000000006e25e8
R8 =0x00000000006e25e8, R9 =0x00000000fffff806, R10=0x000001edc3a3b820, R11=0x0000000000000003
R12=0x0000000000000000, R13=0xaaaaaaaaaaaaaaab, R14=0x0000000100050210, R15=0x00000000877c8a10
RIP=0x00007ffe1c3f78c5, EFLAGS=0x0000000000010a07


Register to memory mapping:

RAX=0x000001edc3a56800 points into unknown readable memory: 0x000001edc3aa8ff0 | f0 8f aa c3 ed 01 00 00
RBX=0x000001edc3a97280 points into unknown readable memory: 0x00007ffe1ca49cf8 | f8 9c a4 1c fe 7f 00 00
RCX=0x0000000000000014 is an unknown value
RDX=0x0 is null
RSP=0x00000067614ff780 points into unknown readable memory: 0x00000000000e4e1c | 1c 4e 0e 00 00 00 00 00
RBP=0x0 is null
RSI=0x000001edc3a82ef0 points into unknown readable memory: 0x000001edc3a937e0 | e0 37 a9 c3 ed 01 00 00
RDI=0x00000000006e25e8 is an unknown value
R8 =0x00000000006e25e8 is an unknown value
R9 =0x00000000fffff806 is pointing into object: [Ljdk.internal.vm.FillerElement; 
{0x00000000ffffcce8} - klass: {type array int}
 - length: 3266
R10=0x000001edc3a3b820 points into unknown readable memory: 0x00007ffe1ca48380 | 80 83 a4 1c fe 7f 00 00
R11=0x0000000000000003 is an unknown value
R12=0x0 is null
R13=0xaaaaaaaaaaaaaaab is an unknown value
R14=0x0000000100050210 is a pointer to class: 
java.lang.BootstrapMethodError {0x0000000100050218}
 - instance size:     5
 - klass size:        78
 - access:            public synchronized 
 - flags:             has_nonstatic_fields 
 - state:             loaded
 - name:              'java/lang/BootstrapMethodError'
 - super:             'java/lang/LinkageError'
 - sub:               
 - arrays:            null
 - methods:           Array<T>(0x000001eddb0bacc8)
 - method ordering:   Array<T>(0x000001eddb000018)
 - local interfaces:  Array<T>(0x000001eddb000058)
 - trans. interfaces: Array<T>(0x000001eddb02ec20)
 - constants:         constant pool [45] {0x000001eddb0baaf8} for 'java/lang/BootstrapMethodError'
 - class loader data:  loader data: 0x000001edc3afb400 of 'bootstrap'
 - inner classes:     Array<T>(0x000001eddb000028)
 - nest members:     Array<T>(0x000001eddb000028)
 - permitted subclasses:     Array<T>(0x000001eddb000028)
 - java mirror:       a 'java/lang/Class'{0x000000008069e968} = 'java/lang/BootstrapMethodError'
 - vtable length      15  (start addr: 0x00000001000503f0)
 - itable length      2 (start addr: 0x0000000100050468)
 - ---- static fields (1 words):
 - private static final 'serialVersionUID' 'J' @112 
 - ---- non-static fields (6 words):
 - private transient 'depth' 'I' @12 
 - private transient 'backtrace' 'Ljava/lang/Object;' @16 
 - private 'detailMessage' 'Ljava/lang/String;' @20 
 - private 'cause' 'Ljava/lang/Throwable;' @24 
 - private 'stackTrace' '[Ljava/lang/StackTraceElement;' @28 
 - private 'suppressedExceptions' 'Ljava/util/List;' @32 
 - non-static oop maps: 16-32 
R15=0x00000000877c8a10 is an oop: java.lang.invoke.MethodType 
{0x00000000877c8a10} - klass: 'java/lang/invoke/MethodType'
 - ---- fields (total size 5 words):
 - private final 'rtype' 'Ljava/lang/Class;' @12  
[error occurred during error reporting (printing register info), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffe1c79ad62]

Top of Stack: (sp=0x00000067614ff780)
0x00000067614ff780:   00000000000e4e1c 00007ffe1c3f78f6
0x00000067614ff790:   00005c5c15991fea 00007ffe1c3f775f
0x00000067614ff7a0:   00000000006e25e8 00007ffe1c3f775f
0x00000067614ff7b0:   000001edc3a97280 00000067614ff950
0x00000067614ff7c0:   0000000100050208 00000067614ff950
0x00000067614ff7d0:   00000000877c8a34 00007ffe1c3f509d
0x00000067614ff7e0:   00000000877c8a1c 00007ffe1c3f594d
0x00000067614ff7f0:   00000000877c8668 0000000100046b78
0x00000067614ff800:   00000067614ff910 0000000000000001
0x00000067614ff810:   00000000877c8a10 00007ffe1c3f4ce2
0x00000067614ff820:   000000010004ff68 0000000000000005
0x00000067614ff830:   00000067614ff950 0000000000000000
0x00000067614ff840:   000001edc3a97280 00007ffe1c414810
0x00000067614ff850:   00000000877c8a10 000001edc3a82f00
0x00000067614ff860:   00000067614ff910 000001edc3a82f00
0x00000067614ff870:   00000000877c8a10 00007ffe1c411061
0x00000067614ff880:   0000000000ef9142 0000000000000000
0x00000067614ff890:   0000000084502af8 00007ffe1c4124fa
0x00000067614ff8a0:   0000000000f00000 00007ffe1c412bec
0x00000067614ff8b0:   00000000877c89e8 0000000000ef9142
0x00000067614ff8c0:   0000000001da73f6 00000000000e4e1c
0x00000067614ff8d0:   0000000000000000 0000000000000001
0x00000067614ff8e0:   000001edc3a97280 00007ffe1c411933
0x00000067614ff8f0:   0000000000000555 0000000000000000
0x00000067614ff900:   00000067613ff901 0000000000000000
0x00000067614ff910:   000001edc3a82ef0 000001edc3a97280
0x00000067614ff920:   0000000087700000 0000000000020000
0x00000067614ff930:   0000000087700000 0000000000020000
0x00000067614ff940:   000000000007270e 0000000001ce8834
0x00000067614ff950:   00007ffe1ca49d18 000001edda872b30
0x00000067614ff960:   0000000000000003 000001edc3a3b820
0x00000067614ff970:   000001edc3a97280 00007ffe1c77fd03 

Instructions: (pc=0x00007ffe1c3f78c5)
0x00007ffe1c3f77c5:   05 75 35 48 8b 83 a8 00 00 00 48 39 83 a0 00 00
0x00007ffe1c3f77d5:   00 73 10 48 8b 83 c0 00 00 00 48 39 83 b8 00 00
0x00007ffe1c3f77e5:   00 72 7f 48 8b cb e8 10 c2 01 00 b0 01 48 8b 5c
0x00007ffe1c3f77f5:   24 30 48 83 c4 20 5f c3 48 8b 53 30 44 8b 82 80
0x00007ffe1c3f7805:   00 00 00 8b 82 00 01 00 00 41 8b c8 2b c8 81 e1
0x00007ffe1c3f7815:   ff ff 01 00 81 f9 fe ff 01 00 72 2c 48 8b cb e8
0x00007ffe1c3f7825:   a7 b5 01 00 48 8b 53 30 44 8b 82 80 00 00 00 8b
0x00007ffe1c3f7835:   82 00 01 00 00 41 8b c8 2b c8 81 e1 ff ff 01 00
0x00007ffe1c3f7845:   81 f9 fe ff 01 00 73 1a 48 8b 82 80 01 00 00 4a
0x00007ffe1c3f7855:   89 3c c0 41 8d 40 01 25 ff ff 01 00 89 82 80 00
0x00007ffe1c3f7865:   00 00 48 8b 5c 24 30 b0 01 48 83 c4 20 5f c3 cc
0x00007ffe1c3f7875:   cc cc cc cc cc cc cc cc cc cc cc 48 89 6c 24 10
0x00007ffe1c3f7885:   48 89 74 24 18 57 48 83 ec 20 4c 8b 51 08 48 8b
0x00007ffe1c3f7895:   f1 4d 8b c8 8b ea 49 8b f8 41 8b 8a 18 02 00 00
0x00007ffe1c3f78a5:   49 8b 82 10 02 00 00 48 d3 e0 8b 0d 9b d7 87 00
0x00007ffe1c3f78b5:   4c 2b c8 49 8b 82 f8 01 00 00 49 d3 e9 45 8b c9
0x00007ffe1c3f78c5:   4a 8b 04 c8 48 8b 40 68 4c 3b c0 72 12 32 c0 48
0x00007ffe1c3f78d5:   8b 6c 24 38 48 8b 74 24 40 48 83 c4 20 5f c3 48
0x00007ffe1c3f78e5:   8b 46 10 48 8d 4e 10 48 8b d7 48 89 5c 24 30 ff
0x00007ffe1c3f78f5:   10 8b 4e 28 48 8b d7 48 2b 56 18 48 8b 46 30 48
0x00007ffe1c3f7905:   c1 ea 03 48 d3 ea 48 8b ca 48 c1 e9 06 4c 8d 04
0x00007ffe1c3f7915:   c8 0f b6 ca 49 8b 00 80 e1 3f ba 01 00 00 00 48
0x00007ffe1c3f7925:   d3 e2 48 8b c8 48 0b ca 48 3b c8 74 12 f0 49 0f
0x00007ffe1c3f7935:   b1 08 74 25 48 8b c8 48 0b ca 48 3b c8 75 ee 32
0x00007ffe1c3f7945:   db 48 8b 6c 24 38 0f b6 c3 48 8b 5c 24 30 48 8b
0x00007ffe1c3f7955:   74 24 40 48 83 c4 20 5f c3 80 3d ab a8 81 00 00
0x00007ffe1c3f7965:   b3 01 74 15 8b 57 08 8b 0d a6 95 81 00 48 d3 e2
0x00007ffe1c3f7975:   48 03 15 94 95 81 00 eb 04 48 8b 57 08 48 8b cf
0x00007ffe1c3f7985:   e8 66 27 e2 ff 48 8b 8e b8 02 00 00 4c 8b d8 4c
0x00007ffe1c3f7995:   8b 0c e9 49 8b 51 18 8b 8a 18 02 00 00 4c 8b 82
0x00007ffe1c3f79a5:   10 02 00 00 41 8b 51 60 49 d3 e0 8b 0d 9a d6 87
0x00007ffe1c3f79b5:   00 49 2b f8 48 d3 ef 44 8b d7 49 23 d2 48 c1 e2 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x00000000000e4e1c is an unknown value
stack at sp + 1 slots: 0x00007ffe1c3f78f6 jvm.dll
stack at sp + 2 slots: 0x00005c5c15991fea is an unknown value
stack at sp + 3 slots: 0x00007ffe1c3f775f jvm.dll
stack at sp + 4 slots: 0x00000000006e25e8 is an unknown value
stack at sp + 5 slots: 0x00007ffe1c3f775f jvm.dll
stack at sp + 6 slots: 0x000001edc3a97280 points into unknown readable memory: 0x00007ffe1ca49cf8 | f8 9c a4 1c fe 7f 00 00
stack at sp + 7 slots: 0x00000067614ff950 points into unknown readable memory: 0x00007ffe1ca49d18 | 18 9d a4 1c fe 7f 00 00


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001edec257a60, length=82, elements={
0x000001edc3a2a320, 0x000001eddaa3a900, 0x000001eddaa82320, 0x000001eddaa8ef40,
0x000001eddaa6ae40, 0x000001eddaa6b4a0, 0x000001eddaa6c690, 0x000001eddaa6f420,
0x000001eddaa70650, 0x000001eddaadc630, 0x000001eddad824f0, 0x000001ede0843780,
0x000001ede096d200, 0x000001ede0990710, 0x000001ede0a0f9b0, 0x000001ede0a127a0,
0x000001ede0a10040, 0x000001ede0a12e30, 0x000001ede0a113f0, 0x000001ede33a7bd0,
0x000001ede222b580, 0x000001ede59d73c0, 0x000001ede59db560, 0x000001ede59d8e00,
0x000001ede59de9e0, 0x000001ede59d8770, 0x000001edeb5b55e0, 0x000001edeb5b48c0,
0x000001edeb5b5c70, 0x000001edeb5b76b0, 0x000001edeb5b90f0, 0x000001edeb5b4230,
0x000001edeb5ba4a0, 0x000001edeb5b6990, 0x000001edeb5b9780, 0x000001edeb5b7d40,
0x000001edeb5b83d0, 0x000001edeb5b3ba0, 0x000001edeb5b9e10, 0x000001edeb5bcc00,
0x000001edeb5bd290, 0x000001edeb5bab30, 0x000001edeb5bb1c0, 0x000001edeb5bb850,
0x000001edeb5bbee0, 0x000001edeb5b6300, 0x000001edeb5bd920, 0x000001edeb5bdfb0,
0x000001edeb5b7020, 0x000001edeb5c0710, 0x000001edeb5be640, 0x000001edeb5c0080,
0x000001edeb5c2e70, 0x000001edeb5c1ac0, 0x000001edeb5bf9f0, 0x000001edeb5bf360,
0x000001edeb5c0da0, 0x000001edeb5c2150, 0x000001edeb5c27e0, 0x000001ede58d3680,
0x000001ede58d0890, 0x000001ede58ce130, 0x000001ede58cf4e0, 0x000001ede58cfb70,
0x000001ede58d43a0, 0x000001ede58d1c40, 0x000001ede58d22d0, 0x000001ede58d2960,
0x000001ede58d2ff0, 0x000001ede58cdaa0, 0x000001ede58d3d10, 0x000001ede58ce7c0,
0x000001ede58cee50, 0x000001ede58d0200, 0x000001ede58d15b0, 0x000001edeb254ba0,
0x000001edeb253160, 0x000001edeb255f50, 0x000001edeb256c70, 0x000001edeb250370,
0x000001edeb257990, 0x000001ede46dcf90
}

Java Threads: ( => current thread )
  0x000001edc3a2a320 JavaThread "main"                              [_thread_blocked, id=7248, stack(0x0000006761100000,0x0000006761200000) (1024K)]
  0x000001eddaa3a900 JavaThread "Reference Handler"          daemon [_thread_blocked, id=1460, stack(0x0000006761900000,0x0000006761a00000) (1024K)]
  0x000001eddaa82320 JavaThread "Finalizer"                  daemon [_thread_blocked, id=8516, stack(0x0000006761a00000,0x0000006761b00000) (1024K)]
  0x000001eddaa8ef40 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=2212, stack(0x0000006761b00000,0x0000006761c00000) (1024K)]
  0x000001eddaa6ae40 JavaThread "Attach Listener"            daemon [_thread_blocked, id=3664, stack(0x0000006761c00000,0x0000006761d00000) (1024K)]
  0x000001eddaa6b4a0 JavaThread "Service Thread"             daemon [_thread_blocked, id=10548, stack(0x0000006761d00000,0x0000006761e00000) (1024K)]
  0x000001eddaa6c690 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=6996, stack(0x0000006761e00000,0x0000006761f00000) (1024K)]
  0x000001eddaa6f420 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=4720, stack(0x0000006761f00000,0x0000006762000000) (1024K)]
  0x000001eddaa70650 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=8512, stack(0x0000006762000000,0x0000006762100000) (1024K)]
  0x000001eddaadc630 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=2236, stack(0x0000006762100000,0x0000006762200000) (1024K)]
  0x000001eddad824f0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=8300, stack(0x0000006762200000,0x0000006762300000) (1024K)]
  0x000001ede0843780 JavaThread "Daemon health stats"               [_thread_blocked, id=4236, stack(0x0000006762700000,0x0000006762800000) (1024K)]
  0x000001ede096d200 JavaThread "Incoming local TCP Connector on port 56015"        [_thread_in_native, id=10684, stack(0x0000006762800000,0x0000006762900000) (1024K)]
  0x000001ede0990710 JavaThread "Daemon periodic checks"            [_thread_blocked, id=8976, stack(0x0000006762900000,0x0000006762a00000) (1024K)]
  0x000001ede0a0f9b0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=4280, stack(0x0000006763100000,0x0000006763200000) (1024K)]
  0x000001ede0a127a0 JavaThread "File lock request listener"        [_thread_in_native, id=3964, stack(0x0000006763200000,0x0000006763300000) (1024K)]
  0x000001ede0a10040 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileHashes)"        [_thread_blocked, id=6132, stack(0x0000006763300000,0x0000006763400000) (1024K)]
  0x000001ede0a12e30 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileContent)"        [_thread_blocked, id=11624, stack(0x0000006763400000,0x0000006763500000) (1024K)]
  0x000001ede0a113f0 JavaThread "File watcher server"        daemon [_thread_blocked, id=7720, stack(0x0000006763700000,0x0000006763800000) (1024K)]
  0x000001ede33a7bd0 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=11140, stack(0x0000006763800000,0x0000006763900000) (1024K)]
  0x000001ede222b580 JavaThread "Memory manager"                    [_thread_blocked, id=8528, stack(0x0000006764800000,0x0000006764900000) (1024K)]
  0x000001ede59d73c0 JavaThread "RMI Scheduler(0)"           daemon [_thread_blocked, id=2680, stack(0x0000006766700000,0x0000006766800000) (1024K)]
  0x000001ede59db560 JavaThread "RMI GC Daemon"              daemon [_thread_blocked, id=5600, stack(0x0000006767000000,0x0000006767100000) (1024K)]
  0x000001ede59d8e00 JavaThread "RMI TCP Accept-0"           daemon [_thread_in_native, id=4816, stack(0x0000006767100000,0x0000006767200000) (1024K)]
  0x000001ede59de9e0 JavaThread "RMI Reaper"                        [_thread_blocked, id=10404, stack(0x0000006767200000,0x0000006767300000) (1024K)]
  0x000001ede59d8770 JavaThread "Cache worker for Java compile cache (C:\Users\<USER>\.gradle\caches\8.11.1\javaCompile)"        [_thread_blocked, id=2872, stack(0x0000006760f00000,0x0000006761000000) (1024K)]
  0x000001edeb5b55e0 JavaThread "Daemon Thread 2"                   [_thread_blocked, id=8672, stack(0x0000006760e00000,0x0000006760f00000) (1024K)]
  0x000001edeb5b48c0 JavaThread "Handler for socket connection from /127.0.0.1:56015 to /127.0.0.1:56755"        [_thread_in_native, id=10496, stack(0x0000006761000000,0x0000006761100000) (1024K)]
  0x000001edeb5b5c70 JavaThread "Cancel handler"                    [_thread_blocked, id=72, stack(0x0000006762600000,0x0000006762700000) (1024K)]
  0x000001edeb5b76b0 JavaThread "Daemon worker Thread 2"            [_thread_blocked, id=9852, stack(0x0000006762a00000,0x0000006762b00000) (1024K)]
  0x000001edeb5b90f0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:56015 to /127.0.0.1:56755"        [_thread_blocked, id=6648, stack(0x0000006762b00000,0x0000006762c00000) (1024K)]
  0x000001edeb5b4230 JavaThread "Stdin handler"                     [_thread_blocked, id=12228, stack(0x0000006762c00000,0x0000006762d00000) (1024K)]
  0x000001edeb5ba4a0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=11992, stack(0x0000006762d00000,0x0000006762e00000) (1024K)]
  0x000001edeb5b6990 JavaThread "Cache worker for file hash cache (E:\dev_pigmomo\yhhelper2025\.gradle\8.11.1\fileHashes)"        [_thread_blocked, id=10196, stack(0x0000006762e00000,0x0000006762f00000) (1024K)]
  0x000001edeb5b9780 JavaThread "Cache worker for Build Output Cleanup Cache (E:\dev_pigmomo\yhhelper2025\.gradle\buildOutputCleanup)"        [_thread_blocked, id=1756, stack(0x0000006762f00000,0x0000006763000000) (1024K)]
  0x000001edeb5b7d40 JavaThread "Cache worker for checksums cache (E:\dev_pigmomo\yhhelper2025\.gradle\8.11.1\checksums)"        [_thread_blocked, id=2216, stack(0x0000006763000000,0x0000006763100000) (1024K)]
  0x000001edeb5b83d0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.11.1\md-rule)"        [_thread_blocked, id=2152, stack(0x0000006763500000,0x0000006763600000) (1024K)]
  0x000001edeb5b3ba0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.11.1\md-supplier)"        [_thread_blocked, id=10640, stack(0x0000006763600000,0x0000006763700000) (1024K)]
  0x000001edeb5b9e10 JavaThread "Unconstrained build operations"        [_thread_blocked, id=9520, stack(0x0000006763a00000,0x0000006763b00000) (1024K)]
  0x000001edeb5bcc00 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=6368, stack(0x0000006763b00000,0x0000006763c00000) (1024K)]
  0x000001edeb5bd290 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=12136, stack(0x0000006763c00000,0x0000006763d00000) (1024K)]
  0x000001edeb5bab30 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=4916, stack(0x0000006763d00000,0x0000006763e00000) (1024K)]
  0x000001edeb5bb1c0 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=3552, stack(0x0000006763e00000,0x0000006763f00000) (1024K)]
  0x000001edeb5bb850 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=6316, stack(0x0000006763f00000,0x0000006764000000) (1024K)]
  0x000001edeb5bbee0 JavaThread "build event listener"              [_thread_blocked, id=8556, stack(0x0000006763900000,0x0000006763a00000) (1024K)]
  0x000001edeb5b6300 JavaThread "build event listener"              [_thread_blocked, id=12120, stack(0x0000006764000000,0x0000006764100000) (1024K)]
  0x000001edeb5bd920 JavaThread "included builds"                   [_thread_blocked, id=2192, stack(0x0000006764200000,0x0000006764300000) (1024K)]
  0x000001edeb5bdfb0 JavaThread "Execution worker"                  [_thread_blocked, id=228, stack(0x0000006764300000,0x0000006764400000) (1024K)]
  0x000001edeb5b7020 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=9320, stack(0x0000006764400000,0x0000006764500000) (1024K)]
  0x000001edeb5c0710 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=10860, stack(0x0000006764500000,0x0000006764600000) (1024K)]
  0x000001edeb5be640 JavaThread "Cache worker for execution history cache (E:\dev_pigmomo\yhhelper2025\.gradle\8.11.1\executionHistory)"        [_thread_blocked, id=5392, stack(0x0000006764600000,0x0000006764700000) (1024K)]
  0x000001edeb5c0080 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=6820, stack(0x0000006764700000,0x0000006764800000) (1024K)]
  0x000001edeb5c2e70 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=11840, stack(0x0000006764900000,0x0000006764a00000) (1024K)]
  0x000001edeb5c1ac0 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=4440, stack(0x0000006764a00000,0x0000006764b00000) (1024K)]
  0x000001edeb5bf9f0 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=1116, stack(0x0000006764b00000,0x0000006764c00000) (1024K)]
  0x000001edeb5bf360 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=4620, stack(0x0000006764100000,0x0000006764200000) (1024K)]
  0x000001edeb5c0da0 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=8292, stack(0x0000006764c00000,0x0000006764d00000) (1024K)]
  0x000001edeb5c2150 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=10580, stack(0x0000006764d00000,0x0000006764e00000) (1024K)]
  0x000001edeb5c27e0 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=7844, stack(0x0000006764e00000,0x0000006764f00000) (1024K)]
  0x000001ede58d3680 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=9876, stack(0x0000006764f00000,0x0000006765000000) (1024K)]
  0x000001ede58d0890 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=3328, stack(0x0000006765000000,0x0000006765100000) (1024K)]
  0x000001ede58ce130 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=10956, stack(0x0000006765100000,0x0000006765200000) (1024K)]
  0x000001ede58cf4e0 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=11560, stack(0x0000006765200000,0x0000006765300000) (1024K)]
  0x000001ede58cfb70 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=3356, stack(0x0000006765300000,0x0000006765400000) (1024K)]
  0x000001ede58d43a0 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=10236, stack(0x0000006765400000,0x0000006765500000) (1024K)]
  0x000001ede58d1c40 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=9296, stack(0x0000006765500000,0x0000006765600000) (1024K)]
  0x000001ede58d22d0 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=10788, stack(0x0000006765600000,0x0000006765700000) (1024K)]
  0x000001ede58d2960 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=4932, stack(0x0000006765800000,0x0000006765900000) (1024K)]
  0x000001ede58d2ff0 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=10128, stack(0x0000006765900000,0x0000006765a00000) (1024K)]
  0x000001ede58cdaa0 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=6992, stack(0x0000006765a00000,0x0000006765b00000) (1024K)]
  0x000001ede58d3d10 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=11800, stack(0x0000006765b00000,0x0000006765c00000) (1024K)]
  0x000001ede58ce7c0 JavaThread "RMI TCP Connection(16)-127.0.0.1" daemon [_thread_in_native, id=9360, stack(0x0000006765d00000,0x0000006765e00000) (1024K)]
  0x000001ede58cee50 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=11896, stack(0x0000006765700000,0x0000006765800000) (1024K)]
  0x000001ede58d0200 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=5428, stack(0x0000006765f00000,0x0000006766000000) (1024K)]
  0x000001ede58d15b0 JavaThread "RMI TCP Connection(17)-127.0.0.1" daemon [_thread_in_native, id=12008, stack(0x0000006766100000,0x0000006766200000) (1024K)]
  0x000001edeb254ba0 JavaThread "RMI TCP Connection(18)-127.0.0.1" daemon [_thread_in_native, id=4412, stack(0x0000006765e00000,0x0000006765f00000) (1024K)]
  0x000001edeb253160 JavaThread "Build operations"                  [_thread_blocked, id=9796, stack(0x0000006766200000,0x0000006766300000) (1024K)]
  0x000001edeb255f50 JavaThread "Build operations Thread 2"         [_thread_blocked, id=4424, stack(0x0000006766300000,0x0000006766400000) (1024K)]
  0x000001edeb256c70 JavaThread "Build operations Thread 3"         [_thread_blocked, id=10060, stack(0x0000006766400000,0x0000006766500000) (1024K)]
  0x000001edeb250370 JavaThread "WorkerExecutor Queue Thread 4"        [_thread_blocked, id=4908, stack(0x0000006766600000,0x0000006766700000) (1024K)]
  0x000001edeb257990 JavaThread "WorkerExecutor Queue Thread 5"        [_thread_blocked, id=4692, stack(0x0000006766800000,0x0000006766900000) (1024K)]
  0x000001ede46dcf90 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=3804, stack(0x0000006766900000,0x0000006766a00000) (1024K)]
Total: 82

Other Threads:
  0x000001edda8c1c80 VMThread "VM Thread"                           [id=5468, stack(0x0000006761800000,0x0000006761900000) (1024K)]
  0x000001edda82b4d0 WatcherThread "VM Periodic Task Thread"        [id=11224, stack(0x0000006761700000,0x0000006761800000) (1024K)]
  0x000001edc3a818e0 WorkerThread "GC Thread#0"                     [id=3148, stack(0x0000006761200000,0x0000006761300000) (1024K)]
  0x000001eddf1f75b0 WorkerThread "GC Thread#1"                     [id=1912, stack(0x0000006762300000,0x0000006762400000) (1024K)]
  0x000001eddf1f7950 WorkerThread "GC Thread#2"                     [id=2312, stack(0x0000006762400000,0x0000006762500000) (1024K)]
  0x000001eddf1ebba0 WorkerThread "GC Thread#3"                     [id=12064, stack(0x0000006762500000,0x0000006762600000) (1024K)]
  0x000001edc3a937e0 ConcurrentGCThread "G1 Main Marker"            [id=10232, stack(0x0000006761300000,0x0000006761400000) (1024K)]
=>0x000001edc3a95370 WorkerThread "G1 Conc#0"                       [id=8940, stack(0x0000006761400000,0x0000006761500000) (1024K)]
  0x000001edda6f20c0 ConcurrentGCThread "G1 Refine#0"               [id=4024, stack(0x0000006761500000,0x0000006761600000) (1024K)]
  0x000001edc3afdd20 ConcurrentGCThread "G1 Service"                [id=10032, stack(0x0000006761600000,0x0000006761700000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  2931546 26035       4       com.android.tools.r8.internal.Q5::a (49 bytes)
C2 CompilerThread1  2931546 26085 %     4       com.android.tools.r8.internal.A9::a @ 134 (1719 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffe1cc8d460] Heap_lock - owner thread: 0x000001ede58d0200

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x0000000140000000, reserved size: 1073741824
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x140000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 4 total, 4 available
 Memory: 8081M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 323584K, used 293205K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 123 young (125952K), 12 survivors (12288K)
 Metaspace       used 150696K, committed 153088K, reserved 1245184K
  class space    used 20337K, committed 21504K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080100000| PB 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080200000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080300000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%| O|  |TAMS 0x0000000080400000| PB 0x0000000080300000| Untracked 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080600000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000| PB 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%|HS|  |TAMS 0x0000000080800000| PB 0x0000000080700000| Complete 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000| PB 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000| PB 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000| PB 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000| PB 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000| PB 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000| PB 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000| PB 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000| PB 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000| PB 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000| PB 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000082000000| PB 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082100000| PB 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082200000| PB 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082300000| PB 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000| PB 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082500000| PB 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000| PB 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000| PB 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082800000| PB 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082900000| PB 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082a00000| PB 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082b00000| PB 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000| PB 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082d00000| PB 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082e00000| PB 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082f00000| PB 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000083000000| PB 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083100000| PB 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%| O|  |TAMS 0x0000000083200000| PB 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%| O|  |TAMS 0x0000000083300000| PB 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083400000| PB 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%|HS|  |TAMS 0x0000000083500000| PB 0x0000000083400000| Complete 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%|HS|  |TAMS 0x0000000083600000| PB 0x0000000083500000| Complete 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%|HS|  |TAMS 0x0000000083700000| PB 0x0000000083600000| Complete 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%|HC|  |TAMS 0x0000000083800000| PB 0x0000000083700000| Complete 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%|HS|  |TAMS 0x0000000083900000| PB 0x0000000083800000| Complete 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%|HC|  |TAMS 0x0000000083a00000| PB 0x0000000083900000| Complete 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%|HS|  |TAMS 0x0000000083b00000| PB 0x0000000083a00000| Complete 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%|HS|  |TAMS 0x0000000083c00000| PB 0x0000000083b00000| Complete 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%|HC|  |TAMS 0x0000000083d00000| PB 0x0000000083c00000| Complete 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%|HC|  |TAMS 0x0000000083e00000| PB 0x0000000083d00000| Complete 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%|HS|  |TAMS 0x0000000083f00000| PB 0x0000000083e00000| Complete 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000084000000| PB 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%|HS|  |TAMS 0x0000000084100000| PB 0x0000000084000000| Complete 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%|HS|  |TAMS 0x0000000084200000| PB 0x0000000084100000| Complete 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000| PB 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084400000| PB 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084500000| PB 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084600000| PB 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084700000| PB 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084800000| PB 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084900000| PB 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%|HS|  |TAMS 0x0000000084a00000| PB 0x0000000084900000| Complete 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084b00000| PB 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000| PB 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000| PB 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000| PB 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%|HS|  |TAMS 0x0000000084f00000| PB 0x0000000084e00000| Complete 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%|HC|  |TAMS 0x0000000085000000| PB 0x0000000084f00000| Complete 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%|HC|  |TAMS 0x0000000085100000| PB 0x0000000085000000| Complete 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085200000| PB 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085300000| PB 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085400000| PB 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085500000| PB 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085600000| PB 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085800000| PB 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085d00000| PB 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085e00000| PB 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085f00000| PB 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000086000000| PB 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086100000| PB 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086200000| PB 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086300000| PB 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086500000| PB 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086600000| PB 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086700000| PB 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086800000| PB 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086900000| PB 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086a00000| PB 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086b00000| PB 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086d00000| PB 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086e00000| PB 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086f00000| PB 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000087000000| PB 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087100000| PB 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087200000| PB 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087300000| PB 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087400000| PB 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|  |TAMS 0x0000000087500000| PB 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087700000| PB 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087800000| PB 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087900000| PB 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087a00000| PB 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087b00000| PB 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087c00000| PB 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087d00000| PB 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087e00000| PB 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087f00000| PB 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000088000000| PB 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| O|  |TAMS 0x0000000088100000| PB 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088200000| PB 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| O|  |TAMS 0x0000000088300000| PB 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| O|  |TAMS 0x0000000088400000| PB 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| O|  |TAMS 0x0000000088500000| PB 0x0000000088400000| Untracked 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| O|  |TAMS 0x0000000088600000| PB 0x0000000088500000| Untracked 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| O|  |TAMS 0x0000000088700000| PB 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%| O|  |TAMS 0x0000000088800000| PB 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088900000| PB 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088a00000| PB 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088b00000| PB 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088c00000| PB 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088d00000| PB 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| O|  |TAMS 0x0000000088e00000| PB 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| O|  |TAMS 0x0000000088f00000| PB 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000088f00000, 0x0000000089000000|  0%| F|  |TAMS 0x0000000088f00000| PB 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089000000, 0x0000000089100000|  0%| F|  |TAMS 0x0000000089000000| PB 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089200000| PB 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089300000| PB 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| O|  |TAMS 0x0000000089400000| PB 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| O|  |TAMS 0x0000000089500000| PB 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%| O|  |TAMS 0x0000000089600000| PB 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| O|  |TAMS 0x0000000089700000| PB 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%| O|  |TAMS 0x0000000089800000| PB 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089900000| PB 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| O|  |TAMS 0x0000000089a00000| PB 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%| O|  |TAMS 0x0000000089b00000| PB 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089c00000| PB 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089d00000| PB 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| O|  |TAMS 0x0000000089e00000| PB 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089f00000| PB 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| O|  |TAMS 0x000000008a000000| PB 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| O|  |TAMS 0x000000008a100000| PB 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a100000, 0x000000008a200000|  0%| F|  |TAMS 0x000000008a100000| PB 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a200000, 0x000000008a300000|  0%| F|  |TAMS 0x000000008a200000| PB 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000| PB 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a400000, 0x000000008a500000|  0%| F|  |TAMS 0x000000008a400000| PB 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a500000, 0x000000008a600000|  0%| F|  |TAMS 0x000000008a500000| PB 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a600000, 0x000000008a700000|  0%| F|  |TAMS 0x000000008a600000| PB 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a700000, 0x000000008a800000|  0%| F|  |TAMS 0x000000008a700000| PB 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a8557b0, 0x000000008a900000| 33%| S|CS|TAMS 0x000000008a800000| PB 0x000000008a800000| Complete 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| S|CS|TAMS 0x000000008a900000| PB 0x000000008a900000| Complete 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| S|CS|TAMS 0x000000008aa00000| PB 0x000000008aa00000| Complete 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| S|CS|TAMS 0x000000008ab00000| PB 0x000000008ab00000| Complete 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| S|CS|TAMS 0x000000008ac00000| PB 0x000000008ac00000| Complete 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| S|CS|TAMS 0x000000008ad00000| PB 0x000000008ad00000| Complete 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| S|CS|TAMS 0x000000008ae00000| PB 0x000000008ae00000| Complete 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%| S|CS|TAMS 0x000000008af00000| PB 0x000000008af00000| Complete 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| S|CS|TAMS 0x000000008b000000| PB 0x000000008b000000| Complete 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%| S|CS|TAMS 0x000000008b100000| PB 0x000000008b100000| Complete 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%| S|CS|TAMS 0x000000008b200000| PB 0x000000008b200000| Complete 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%| S|CS|TAMS 0x000000008b300000| PB 0x000000008b300000| Complete 
| 180|0x000000008b400000, 0x000000008b400000, 0x000000008b500000|  0%| F|  |TAMS 0x000000008b400000| PB 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000| PB 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000| PB 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000| PB 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bb00000, 0x000000008bc00000|  0%| F|  |TAMS 0x000000008bb00000| PB 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bc00000, 0x000000008bd00000|  0%| F|  |TAMS 0x000000008bc00000| PB 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008bd00000, 0x000000008be00000|  0%| F|  |TAMS 0x000000008bd00000| PB 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008be00000, 0x000000008bf00000|  0%| F|  |TAMS 0x000000008be00000| PB 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008bf00000, 0x000000008c000000|  0%| F|  |TAMS 0x000000008bf00000| PB 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c000000, 0x000000008c100000|  0%| F|  |TAMS 0x000000008c000000| PB 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c100000, 0x000000008c200000|  0%| F|  |TAMS 0x000000008c100000| PB 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c200000, 0x000000008c300000|  0%| F|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c300000, 0x000000008c400000|  0%| F|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c400000, 0x000000008c500000|  0%| F|  |TAMS 0x000000008c400000| PB 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c500000, 0x000000008c600000|  0%| F|  |TAMS 0x000000008c500000| PB 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c600000, 0x000000008c700000|  0%| F|  |TAMS 0x000000008c600000| PB 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c700000, 0x000000008c800000|  0%| F|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| E|CS|TAMS 0x000000008c800000| PB 0x000000008c800000| Complete 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| E|CS|TAMS 0x000000008c900000| PB 0x000000008c900000| Complete 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| E|CS|TAMS 0x000000008ca00000| PB 0x000000008ca00000| Complete 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%| E|CS|TAMS 0x000000008cb00000| PB 0x000000008cb00000| Complete 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| E|CS|TAMS 0x000000008cc00000| PB 0x000000008cc00000| Complete 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| E|CS|TAMS 0x000000008cd00000| PB 0x000000008cd00000| Complete 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| E|CS|TAMS 0x000000008ce00000| PB 0x000000008ce00000| Complete 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| E|CS|TAMS 0x000000008cf00000| PB 0x000000008cf00000| Complete 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| E|CS|TAMS 0x000000008d000000| PB 0x000000008d000000| Complete 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| E|CS|TAMS 0x000000008d100000| PB 0x000000008d100000| Complete 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| E|CS|TAMS 0x000000008d200000| PB 0x000000008d200000| Complete 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| E|CS|TAMS 0x000000008d300000| PB 0x000000008d300000| Complete 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| E|CS|TAMS 0x000000008d400000| PB 0x000000008d400000| Complete 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| E|CS|TAMS 0x000000008d500000| PB 0x000000008d500000| Complete 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| E|CS|TAMS 0x000000008d600000| PB 0x000000008d600000| Complete 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| E|CS|TAMS 0x000000008d700000| PB 0x000000008d700000| Complete 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| E|CS|TAMS 0x000000008d800000| PB 0x000000008d800000| Complete 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| E|CS|TAMS 0x000000008d900000| PB 0x000000008d900000| Complete 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| E|CS|TAMS 0x000000008da00000| PB 0x000000008da00000| Complete 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| E|CS|TAMS 0x000000008db00000| PB 0x000000008db00000| Complete 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| E|CS|TAMS 0x000000008dc00000| PB 0x000000008dc00000| Complete 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| E|CS|TAMS 0x000000008dd00000| PB 0x000000008dd00000| Complete 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| E|CS|TAMS 0x000000008de00000| PB 0x000000008de00000| Complete 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| E|CS|TAMS 0x000000008df00000| PB 0x000000008df00000| Complete 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| E|CS|TAMS 0x000000008e000000| PB 0x000000008e000000| Complete 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| E|CS|TAMS 0x000000008e100000| PB 0x000000008e100000| Complete 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| E|CS|TAMS 0x000000008e200000| PB 0x000000008e200000| Complete 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| E|CS|TAMS 0x000000008e300000| PB 0x000000008e300000| Complete 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| E|CS|TAMS 0x000000008e400000| PB 0x000000008e400000| Complete 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| E|CS|TAMS 0x000000008e500000| PB 0x000000008e500000| Complete 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| E|CS|TAMS 0x000000008e600000| PB 0x000000008e600000| Complete 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| E|CS|TAMS 0x000000008e700000| PB 0x000000008e700000| Complete 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| E|CS|TAMS 0x000000008e800000| PB 0x000000008e800000| Complete 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%| E|CS|TAMS 0x000000008e900000| PB 0x000000008e900000| Complete 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| E|CS|TAMS 0x000000008ea00000| PB 0x000000008ea00000| Complete 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%| E|CS|TAMS 0x000000008eb00000| PB 0x000000008eb00000| Complete 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| E|CS|TAMS 0x000000008ec00000| PB 0x000000008ec00000| Complete 
| 237|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%| E|CS|TAMS 0x000000008ed00000| PB 0x000000008ed00000| Complete 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| E|CS|TAMS 0x000000008ee00000| PB 0x000000008ee00000| Complete 
| 239|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%| E|CS|TAMS 0x000000008ef00000| PB 0x000000008ef00000| Complete 
| 240|0x000000008f000000, 0x000000008f100000, 0x000000008f100000|100%| E|CS|TAMS 0x000000008f000000| PB 0x000000008f000000| Complete 
| 241|0x000000008f100000, 0x000000008f200000, 0x000000008f200000|100%| E|CS|TAMS 0x000000008f100000| PB 0x000000008f100000| Complete 
| 242|0x000000008f200000, 0x000000008f300000, 0x000000008f300000|100%| E|CS|TAMS 0x000000008f200000| PB 0x000000008f200000| Complete 
| 243|0x000000008f300000, 0x000000008f400000, 0x000000008f400000|100%| E|CS|TAMS 0x000000008f300000| PB 0x000000008f300000| Complete 
| 244|0x000000008f400000, 0x000000008f500000, 0x000000008f500000|100%| E|CS|TAMS 0x000000008f400000| PB 0x000000008f400000| Complete 
| 245|0x000000008f500000, 0x000000008f600000, 0x000000008f600000|100%| E|CS|TAMS 0x000000008f500000| PB 0x000000008f500000| Complete 
| 246|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%| E|CS|TAMS 0x000000008f600000| PB 0x000000008f600000| Complete 
| 247|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%| E|CS|TAMS 0x000000008f700000| PB 0x000000008f700000| Complete 
| 248|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%| E|CS|TAMS 0x000000008f800000| PB 0x000000008f800000| Complete 
| 249|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%| E|CS|TAMS 0x000000008f900000| PB 0x000000008f900000| Complete 
| 250|0x000000008fa00000, 0x000000008fb00000, 0x000000008fb00000|100%| E|CS|TAMS 0x000000008fa00000| PB 0x000000008fa00000| Complete 
| 251|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%| E|CS|TAMS 0x000000008fb00000| PB 0x000000008fb00000| Complete 
| 252|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%| E|CS|TAMS 0x000000008fc00000| PB 0x000000008fc00000| Complete 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%| E|CS|TAMS 0x000000008fd00000| PB 0x000000008fd00000| Complete 
| 254|0x000000008fe00000, 0x000000008ff00000, 0x000000008ff00000|100%| E|CS|TAMS 0x000000008fe00000| PB 0x000000008fe00000| Complete 
| 255|0x000000008ff00000, 0x0000000090000000, 0x0000000090000000|100%| E|CS|TAMS 0x000000008ff00000| PB 0x000000008ff00000| Complete 
| 256|0x0000000090000000, 0x0000000090100000, 0x0000000090100000|100%| E|CS|TAMS 0x0000000090000000| PB 0x0000000090000000| Complete 
| 257|0x0000000090100000, 0x0000000090200000, 0x0000000090200000|100%| E|CS|TAMS 0x0000000090100000| PB 0x0000000090100000| Complete 
| 258|0x0000000090200000, 0x0000000090300000, 0x0000000090300000|100%| E|CS|TAMS 0x0000000090200000| PB 0x0000000090200000| Complete 
| 259|0x0000000090300000, 0x0000000090400000, 0x0000000090400000|100%| E|CS|TAMS 0x0000000090300000| PB 0x0000000090300000| Complete 
| 260|0x0000000090400000, 0x0000000090500000, 0x0000000090500000|100%| E|CS|TAMS 0x0000000090400000| PB 0x0000000090400000| Complete 
| 261|0x0000000090500000, 0x0000000090600000, 0x0000000090600000|100%| E|CS|TAMS 0x0000000090500000| PB 0x0000000090500000| Complete 
| 262|0x0000000090600000, 0x0000000090700000, 0x0000000090700000|100%| E|CS|TAMS 0x0000000090600000| PB 0x0000000090600000| Complete 
| 263|0x0000000090700000, 0x0000000090800000, 0x0000000090800000|100%| E|CS|TAMS 0x0000000090700000| PB 0x0000000090700000| Complete 
| 264|0x0000000090800000, 0x0000000090900000, 0x0000000090900000|100%| E|CS|TAMS 0x0000000090800000| PB 0x0000000090800000| Complete 
| 265|0x0000000090900000, 0x0000000090a00000, 0x0000000090a00000|100%| E|CS|TAMS 0x0000000090900000| PB 0x0000000090900000| Complete 
| 266|0x0000000090a00000, 0x0000000090b00000, 0x0000000090b00000|100%| E|CS|TAMS 0x0000000090a00000| PB 0x0000000090a00000| Complete 
| 267|0x0000000090b00000, 0x0000000090c00000, 0x0000000090c00000|100%| E|CS|TAMS 0x0000000090b00000| PB 0x0000000090b00000| Complete 
| 268|0x0000000090c00000, 0x0000000090d00000, 0x0000000090d00000|100%| E|CS|TAMS 0x0000000090c00000| PB 0x0000000090c00000| Complete 
| 269|0x0000000090d00000, 0x0000000090e00000, 0x0000000090e00000|100%| E|CS|TAMS 0x0000000090d00000| PB 0x0000000090d00000| Complete 
| 270|0x0000000090e00000, 0x0000000090effff8, 0x0000000090f00000| 99%| E|CS|TAMS 0x0000000090e00000| PB 0x0000000090e00000| Complete 
| 271|0x0000000090f00000, 0x0000000091000000, 0x0000000091000000|100%| E|CS|TAMS 0x0000000090f00000| PB 0x0000000090f00000| Complete 
| 272|0x0000000091000000, 0x0000000091100000, 0x0000000091100000|100%| E|CS|TAMS 0x0000000091000000| PB 0x0000000091000000| Complete 
| 273|0x0000000091100000, 0x0000000091200000, 0x0000000091200000|100%| E|CS|TAMS 0x0000000091100000| PB 0x0000000091100000| Complete 
| 274|0x0000000091200000, 0x0000000091300000, 0x0000000091300000|100%| E|CS|TAMS 0x0000000091200000| PB 0x0000000091200000| Complete 
| 275|0x0000000091300000, 0x0000000091400000, 0x0000000091400000|100%| E|CS|TAMS 0x0000000091300000| PB 0x0000000091300000| Complete 
| 276|0x0000000091400000, 0x0000000091500000, 0x0000000091500000|100%| E|CS|TAMS 0x0000000091400000| PB 0x0000000091400000| Complete 
| 277|0x0000000091500000, 0x0000000091600000, 0x0000000091600000|100%| E|CS|TAMS 0x0000000091500000| PB 0x0000000091500000| Complete 
| 278|0x0000000091600000, 0x0000000091700000, 0x0000000091700000|100%| E|CS|TAMS 0x0000000091600000| PB 0x0000000091600000| Complete 
| 279|0x0000000091700000, 0x0000000091800000, 0x0000000091800000|100%| E|CS|TAMS 0x0000000091700000| PB 0x0000000091700000| Complete 
| 280|0x0000000091800000, 0x0000000091900000, 0x0000000091900000|100%| E|CS|TAMS 0x0000000091800000| PB 0x0000000091800000| Complete 
| 281|0x0000000091900000, 0x0000000091a00000, 0x0000000091a00000|100%| E|CS|TAMS 0x0000000091900000| PB 0x0000000091900000| Complete 
| 282|0x0000000091a00000, 0x0000000091b00000, 0x0000000091b00000|100%| E|CS|TAMS 0x0000000091a00000| PB 0x0000000091a00000| Complete 
| 283|0x0000000091b00000, 0x0000000091c00000, 0x0000000091c00000|100%| E|CS|TAMS 0x0000000091b00000| PB 0x0000000091b00000| Complete 
| 284|0x0000000091c00000, 0x0000000091d00000, 0x0000000091d00000|100%| E|CS|TAMS 0x0000000091c00000| PB 0x0000000091c00000| Complete 
| 285|0x0000000091d00000, 0x0000000091e00000, 0x0000000091e00000|100%| E|CS|TAMS 0x0000000091d00000| PB 0x0000000091d00000| Complete 
| 286|0x0000000091e00000, 0x0000000091f00000, 0x0000000091f00000|100%| E|CS|TAMS 0x0000000091e00000| PB 0x0000000091e00000| Complete 
| 287|0x0000000091f00000, 0x0000000092000000, 0x0000000092000000|100%| E|CS|TAMS 0x0000000091f00000| PB 0x0000000091f00000| Complete 
| 288|0x0000000092000000, 0x0000000092100000, 0x0000000092100000|100%| E|CS|TAMS 0x0000000092000000| PB 0x0000000092000000| Complete 
| 289|0x0000000092100000, 0x0000000092200000, 0x0000000092200000|100%| E|CS|TAMS 0x0000000092100000| PB 0x0000000092100000| Complete 
| 290|0x0000000092200000, 0x0000000092300000, 0x0000000092300000|100%| E|CS|TAMS 0x0000000092200000| PB 0x0000000092200000| Complete 
| 291|0x0000000092300000, 0x0000000092400000, 0x0000000092400000|100%| E|CS|TAMS 0x0000000092300000| PB 0x0000000092300000| Complete 
| 292|0x0000000092400000, 0x0000000092500000, 0x0000000092500000|100%| E|CS|TAMS 0x0000000092400000| PB 0x0000000092400000| Complete 
| 293|0x0000000092500000, 0x0000000092600000, 0x0000000092600000|100%| E|CS|TAMS 0x0000000092500000| PB 0x0000000092500000| Complete 
| 294|0x0000000092600000, 0x0000000092700000, 0x0000000092700000|100%| E|CS|TAMS 0x0000000092600000| PB 0x0000000092600000| Complete 
| 295|0x0000000092700000, 0x0000000092800000, 0x0000000092800000|100%| E|CS|TAMS 0x0000000092700000| PB 0x0000000092700000| Complete 
| 296|0x0000000092800000, 0x0000000092900000, 0x0000000092900000|100%| E|CS|TAMS 0x0000000092800000| PB 0x0000000092800000| Complete 
| 297|0x0000000092900000, 0x0000000092a00000, 0x0000000092a00000|100%| E|CS|TAMS 0x0000000092900000| PB 0x0000000092900000| Complete 
| 298|0x0000000092a00000, 0x0000000092b00000, 0x0000000092b00000|100%| E|CS|TAMS 0x0000000092a00000| PB 0x0000000092a00000| Complete 
| 299|0x0000000092b00000, 0x0000000092c00000, 0x0000000092c00000|100%| E|CS|TAMS 0x0000000092b00000| PB 0x0000000092b00000| Complete 
| 300|0x0000000092c00000, 0x0000000092d00000, 0x0000000092d00000|100%| E|CS|TAMS 0x0000000092c00000| PB 0x0000000092c00000| Complete 
| 301|0x0000000092d00000, 0x0000000092e00000, 0x0000000092e00000|100%| E|CS|TAMS 0x0000000092d00000| PB 0x0000000092d00000| Complete 
| 302|0x0000000092e00000, 0x0000000092f00000, 0x0000000092f00000|100%| E|CS|TAMS 0x0000000092e00000| PB 0x0000000092e00000| Complete 
| 303|0x0000000092f00000, 0x0000000093000000, 0x0000000093000000|100%| E|CS|TAMS 0x0000000092f00000| PB 0x0000000092f00000| Complete 
| 304|0x0000000093000000, 0x0000000093100000, 0x0000000093100000|100%| E|CS|TAMS 0x0000000093000000| PB 0x0000000093000000| Complete 
| 305|0x0000000093100000, 0x0000000093200000, 0x0000000093200000|100%| E|CS|TAMS 0x0000000093100000| PB 0x0000000093100000| Complete 
| 306|0x0000000093200000, 0x0000000093300000, 0x0000000093300000|100%| E|CS|TAMS 0x0000000093200000| PB 0x0000000093200000| Complete 
| 307|0x0000000093300000, 0x0000000093400000, 0x0000000093400000|100%| E|CS|TAMS 0x0000000093300000| PB 0x0000000093300000| Complete 
| 308|0x0000000093400000, 0x0000000093500000, 0x0000000093500000|100%| E|CS|TAMS 0x0000000093400000| PB 0x0000000093400000| Complete 
| 309|0x0000000093500000, 0x0000000093600000, 0x0000000093600000|100%| E|CS|TAMS 0x0000000093500000| PB 0x0000000093500000| Complete 
| 310|0x0000000093600000, 0x0000000093700000, 0x0000000093700000|100%| E|CS|TAMS 0x0000000093600000| PB 0x0000000093600000| Complete 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| O|  |TAMS 0x00000000ffc00000| PB 0x00000000ffb00000| Untracked 
|2044|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| O|  |TAMS 0x00000000ffd00000| PB 0x00000000ffc00000| Untracked 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| O|  |TAMS 0x00000000ffe00000| PB 0x00000000ffd00000| Untracked 
|2046|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| O|  |TAMS 0x00000000fff00000| PB 0x00000000ffe00000| Untracked 
|2047|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| O|  |TAMS 0x0000000100000000| PB 0x00000000fff00000| Untracked 

Card table byte_map: [0x000001edd5f90000,0x000001edd6390000] _byte_map_base: 0x000001edd5b90000

Marking Bits: (CMBitMap*) 0x000001edc3a82f00
 Bits: [0x000001edd6390000, 0x000001edd8390000)

Polling page: 0x000001edc1bc0000

Metaspace:

Usage:
  Non-class:    127.30 MB used.
      Class:     19.86 MB used.
       Both:    147.16 MB used.

Virtual space:
  Non-class space:      192.00 MB reserved,     128.50 MB ( 67%) committed,  3 nodes.
      Class space:        1.00 GB reserved,      21.00 MB (  2%) committed,  1 nodes.
             Both:        1.19 GB reserved,     149.50 MB ( 12%) committed. 

Chunk freelists:
   Non-Class:  14.98 MB
       Class:  11.00 MB
        Both:  25.99 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 242.19 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 4738.
num_arena_deaths: 10.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 2391.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 27.
num_chunks_taken_from_freelist: 11113.
num_chunk_merges: 12.
num_chunk_splits: 7245.
num_chunks_enlarged: 4612.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=15936Kb max_used=15936Kb free=104063Kb
 bounds [0x000001edce660000, 0x000001edcf600000, 0x000001edd5b90000]
CodeHeap 'profiled nmethods': size=120000Kb used=42571Kb max_used=42571Kb free=77428Kb
 bounds [0x000001edc6b90000, 0x000001edc9530000, 0x000001edce0c0000]
CodeHeap 'non-nmethods': size=5760Kb used=2299Kb max_used=2452Kb free=3460Kb
 bounds [0x000001edce0c0000, 0x000001edce340000, 0x000001edce660000]
 total_blobs=20938 nmethods=19944 adapters=896
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 2930.606 Thread 0x000001eddaa6f420 nmethod 25977% 0x000001edcf577890 code [0x000001edcf578400, 0x000001edcf585ba8]
Event: 2930.606 Thread 0x000001eddaa70650 26100       1       com.android.tools.r8.internal.Bo::a (5 bytes)
Event: 2930.606 Thread 0x000001eddaa70650 nmethod 26100 0x000001edcf58d890 code [0x000001edcf58da20, 0x000001edcf58dad0]
Event: 2930.606 Thread 0x000001eddaa6f420 26041       4       com.android.tools.r8.internal.Va::a (1190 bytes)
Event: 2930.610 Thread 0x000001eddaa70650 26101       3       com.android.tools.r8.internal.ZD::y1 (42 bytes)
Event: 2930.611 Thread 0x000001eddaa70650 nmethod 26101 0x000001edc9504590 code [0x000001edc9504780, 0x000001edc9504dd8]
Event: 2930.615 Thread 0x000001eddaa70650 26102       3       java.lang.StringConcatHelper::prepend (22 bytes)
Event: 2930.615 Thread 0x000001eddaa70650 nmethod 26102 0x000001edc9504f10 code [0x000001edc95050e0, 0x000001edc9505370]
Event: 2930.628 Thread 0x000001eddaa70650 26103       3       com.android.tools.r8.graph.B2::a (121 bytes)
Event: 2930.629 Thread 0x000001eddaa70650 nmethod 26103 0x000001edc9505490 code [0x000001edc9505800, 0x000001edc95067e0]
Event: 2930.635 Thread 0x000001eddaa70650 26104       1       com.android.tools.r8.internal.R3::p2 (4 bytes)
Event: 2930.635 Thread 0x000001eddaa70650 nmethod 26104 0x000001edcf58db90 code [0x000001edcf58dd20, 0x000001edcf58dde8]
Event: 2930.636 Thread 0x000001eddaa70650 26105       3       com.android.tools.r8.internal.LH::iterator (9 bytes)
Event: 2930.636 Thread 0x000001eddaa70650 nmethod 26105 0x000001edc9506f90 code [0x000001edc9507140, 0x000001edc95072e0]
Event: 2930.644 Thread 0x000001eddaa70650 26106       2       com.android.tools.r8.internal.Yn0::L0 (2 bytes)
Event: 2930.644 Thread 0x000001eddaa70650 nmethod 26106 0x000001edc9507390 code [0x000001edc9507520, 0x000001edc9507628]
Event: 2930.650 Thread 0x000001eddaa70650 26107 %     3       com.android.tools.r8.internal.XS::b @ 7 (144 bytes)
Event: 2930.651 Thread 0x000001eddaa70650 nmethod 26107% 0x000001edc9507690 code [0x000001edc9507960, 0x000001edc9508a50]
Event: 2930.651 Thread 0x000001eddaa70650 26108       3       com.android.tools.r8.internal.XS::b (144 bytes)
Event: 2930.651 Thread 0x000001eddaa70650 nmethod 26108 0x000001edc9508f90 code [0x000001edc9509260, 0x000001edc950a238]

GC Heap History (20 events):
Event: 111.503 GC heap before
{Heap before GC invocations=74 (full 0):
 garbage-first heap   total 319488K, used 175442K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 15 young (15360K), 2 survivors (2048K)
 Metaspace       used 144769K, committed 147136K, reserved 1179648K
  class space    used 19686K, committed 20928K, reserved 1048576K
}
Event: 111.507 GC heap after
{Heap after GC invocations=75 (full 0):
 garbage-first heap   total 319488K, used 164249K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 144769K, committed 147136K, reserved 1179648K
  class space    used 19686K, committed 20928K, reserved 1048576K
}
Event: 111.779 GC heap before
{Heap before GC invocations=75 (full 0):
 garbage-first heap   total 319488K, used 176537K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 15 young (15360K), 2 survivors (2048K)
 Metaspace       used 144817K, committed 147200K, reserved 1179648K
  class space    used 19686K, committed 20928K, reserved 1048576K
}
Event: 111.783 GC heap after
{Heap after GC invocations=76 (full 0):
 garbage-first heap   total 319488K, used 164562K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 1 young (1024K), 1 survivors (1024K)
 Metaspace       used 144817K, committed 147200K, reserved 1179648K
  class space    used 19686K, committed 20928K, reserved 1048576K
}
Event: 112.164 GC heap before
{Heap before GC invocations=76 (full 0):
 garbage-first heap   total 319488K, used 217810K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 54 young (55296K), 1 survivors (1024K)
 Metaspace       used 144924K, committed 147328K, reserved 1179648K
  class space    used 19689K, committed 20928K, reserved 1048576K
}
Event: 112.167 GC heap after
{Heap after GC invocations=77 (full 0):
 garbage-first heap   total 319488K, used 163606K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 1 young (1024K), 1 survivors (1024K)
 Metaspace       used 144924K, committed 147328K, reserved 1179648K
  class space    used 19689K, committed 20928K, reserved 1048576K
}
Event: 113.418 GC heap before
{Heap before GC invocations=77 (full 0):
 garbage-first heap   total 319488K, used 263958K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 101 young (103424K), 1 survivors (1024K)
 Metaspace       used 146388K, committed 148800K, reserved 1179648K
  class space    used 19875K, committed 21120K, reserved 1048576K
}
Event: 113.424 GC heap after
{Heap after GC invocations=78 (full 0):
 garbage-first heap   total 319488K, used 169081K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 146388K, committed 148800K, reserved 1179648K
  class space    used 19875K, committed 21120K, reserved 1048576K
}
Event: 721.593 GC heap before
{Heap before GC invocations=79 (full 0):
 garbage-first heap   total 319488K, used 317561K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 82 young (83968K), 6 survivors (6144K)
 Metaspace       used 149746K, committed 152064K, reserved 1245184K
  class space    used 20328K, committed 21504K, reserved 1048576K
}
Event: 721.608 GC heap after
{Heap after GC invocations=80 (full 0):
 garbage-first heap   total 323584K, used 167514K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 149746K, committed 152064K, reserved 1245184K
  class space    used 20328K, committed 21504K, reserved 1048576K
}
Event: 2886.238 GC heap before
{Heap before GC invocations=80 (full 0):
 garbage-first heap   total 323584K, used 220762K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 56 young (57344K), 4 survivors (4096K)
 Metaspace       used 149961K, committed 152320K, reserved 1245184K
  class space    used 20333K, committed 21504K, reserved 1048576K
}
Event: 2886.387 GC heap after
{Heap after GC invocations=81 (full 0):
 garbage-first heap   total 323584K, used 164066K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 149961K, committed 152320K, reserved 1245184K
  class space    used 20333K, committed 21504K, reserved 1048576K
}
Event: 2888.992 GC heap before
{Heap before GC invocations=81 (full 0):
 garbage-first heap   total 323584K, used 205026K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 45 young (46080K), 4 survivors (4096K)
 Metaspace       used 150078K, committed 152384K, reserved 1245184K
  class space    used 20333K, committed 21504K, reserved 1048576K
}
Event: 2889.072 GC heap after
{Heap after GC invocations=82 (full 0):
 garbage-first heap   total 323584K, used 165906K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 150078K, committed 152384K, reserved 1245184K
  class space    used 20333K, committed 21504K, reserved 1048576K
}
Event: 2890.465 GC heap before
{Heap before GC invocations=82 (full 0):
 garbage-first heap   total 323584K, used 205842K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 44 young (45056K), 5 survivors (5120K)
 Metaspace       used 150344K, committed 152704K, reserved 1245184K
  class space    used 20335K, committed 21504K, reserved 1048576K
}
Event: 2890.477 GC heap after
{Heap after GC invocations=83 (full 0):
 garbage-first heap   total 323584K, used 169208K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 150344K, committed 152704K, reserved 1245184K
  class space    used 20335K, committed 21504K, reserved 1048576K
}
Event: 2891.984 GC heap before
{Heap before GC invocations=83 (full 0):
 garbage-first heap   total 323584K, used 195832K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 33 young (33792K), 6 survivors (6144K)
 Metaspace       used 150422K, committed 152768K, reserved 1245184K
  class space    used 20336K, committed 21504K, reserved 1048576K
}
Event: 2892.049 GC heap after
{Heap after GC invocations=84 (full 0):
 garbage-first heap   total 323584K, used 171287K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 150422K, committed 152768K, reserved 1245184K
  class space    used 20336K, committed 21504K, reserved 1048576K
}
Event: 2929.913 GC heap before
{Heap before GC invocations=84 (full 0):
 garbage-first heap   total 323584K, used 300311K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 131 young (134144K), 4 survivors (4096K)
 Metaspace       used 150689K, committed 153088K, reserved 1245184K
  class space    used 20337K, committed 21504K, reserved 1048576K
}
Event: 2930.048 GC heap after
{Heap after GC invocations=85 (full 0):
 garbage-first heap   total 323584K, used 179541K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 150689K, committed 153088K, reserved 1245184K
  class space    used 20337K, committed 21504K, reserved 1048576K
}

Dll operation events (3 events):
Event: 0.007 Loaded shared library C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\java.dll
Event: 0.011 Loaded shared library C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\zip.dll
Event: 0.426 Loaded shared library C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 2930.496 Thread 0x000001ede58d0200 DEOPT PACKING pc=0x000001edc8c8e61a sp=0x0000006765ffb3b0
Event: 2930.497 Thread 0x000001ede58d0200 DEOPT UNPACKING pc=0x000001edce114e42 sp=0x0000006765ffaaf0 mode 0
Event: 2930.519 Thread 0x000001ede58d2960 DEOPT PACKING pc=0x000001edc8c8e61a sp=0x00000067658fb340
Event: 2930.519 Thread 0x000001ede58d2960 DEOPT UNPACKING pc=0x000001edce114e42 sp=0x00000067658faa80 mode 0
Event: 2930.549 Thread 0x000001ede58d0200 DEOPT PACKING pc=0x000001edc89ce0c1 sp=0x0000006765ffc030
Event: 2930.549 Thread 0x000001ede58d0200 DEOPT UNPACKING pc=0x000001edce114e42 sp=0x0000006765ffb568 mode 0
Event: 2930.551 Thread 0x000001ede58d0200 DEOPT PACKING pc=0x000001edc8c8e61a sp=0x0000006765ffb3b0
Event: 2930.551 Thread 0x000001ede58d0200 DEOPT UNPACKING pc=0x000001edce114e42 sp=0x0000006765ffaaf0 mode 0
Event: 2930.567 Thread 0x000001ede58d2960 DEOPT PACKING pc=0x000001edc8c8e783 sp=0x00000067658fb340
Event: 2930.567 Thread 0x000001ede58d2960 DEOPT UNPACKING pc=0x000001edce114e42 sp=0x00000067658faa80 mode 0
Event: 2930.611 Thread 0x000001ede58d0200 DEOPT PACKING pc=0x000001edc8c8e61a sp=0x0000006765ffb6f0
Event: 2930.611 Thread 0x000001ede58d0200 DEOPT UNPACKING pc=0x000001edce114e42 sp=0x0000006765ffae30 mode 0
Event: 2930.623 Thread 0x000001ede58d0200 DEOPT PACKING pc=0x000001edc8c8e61a sp=0x0000006765ffb3b0
Event: 2930.623 Thread 0x000001ede58d0200 DEOPT UNPACKING pc=0x000001edce114e42 sp=0x0000006765ffaaf0 mode 0
Event: 2930.641 Thread 0x000001ede58d2960 DEOPT PACKING pc=0x000001edc89ce0c1 sp=0x00000067658fbfc0
Event: 2930.641 Thread 0x000001ede58d2960 DEOPT UNPACKING pc=0x000001edce114e42 sp=0x00000067658fb4f8 mode 0
Event: 2930.651 Thread 0x000001ede58d0200 DEOPT PACKING pc=0x000001edc8323fa0 sp=0x0000006765ffc5c0
Event: 2930.651 Thread 0x000001ede58d0200 DEOPT UNPACKING pc=0x000001edce114e42 sp=0x0000006765ffbae0 mode 0
Event: 2930.658 Thread 0x000001ede58d0200 DEOPT PACKING pc=0x000001edc8c8e783 sp=0x0000006765ffb3b0
Event: 2930.658 Thread 0x000001ede58d0200 DEOPT UNPACKING pc=0x000001edce114e42 sp=0x0000006765ffaaf0 mode 0

Classes loaded (20 events):
Event: 117.440 Loading class sun/nio/fs/WindowsFileAttributeViews$Dos
Event: 117.441 Loading class sun/nio/fs/WindowsFileAttributeViews$Dos done
Event: 117.596 Loading class java/util/concurrent/CompletableFuture$AsyncRun
Event: 117.596 Loading class java/util/concurrent/CompletableFuture$AsyncRun done
Event: 117.700 Loading class java/lang/StackStreamFactory
Event: 117.701 Loading class java/lang/StackStreamFactory done
Event: 117.701 Loading class java/lang/StackWalker$ExtendedOption
Event: 117.701 Loading class java/lang/StackWalker$ExtendedOption done
Event: 117.701 Loading class java/lang/StackStreamFactory$StackFrameTraverser
Event: 117.701 Loading class java/lang/StackStreamFactory$StackFrameTraverser done
Event: 117.701 Loading class java/lang/StackStreamFactory$WalkerState
Event: 117.701 Loading class java/lang/StackStreamFactory$WalkerState done
Event: 117.701 Loading class java/lang/StackStreamFactory$StackFrameTraverser$StackFrameBuffer
Event: 117.701 Loading class java/lang/StackStreamFactory$FrameBuffer
Event: 117.701 Loading class java/lang/StackStreamFactory$FrameBuffer done
Event: 117.701 Loading class java/lang/StackStreamFactory$StackFrameTraverser$StackFrameBuffer done
Event: 117.702 Loading class java/util/logging/LogRecord
Event: 117.702 Loading class java/util/logging/LogRecord done
Event: 2884.544 Loading class jdk/internal/math/FormattedFPDecimal
Event: 2884.548 Loading class jdk/internal/math/FormattedFPDecimal done

Classes unloaded (5 events):
Event: 104.449 Thread 0x000001edda8c1c80 Unloading class 0x000000010132c000 'java/lang/invoke/LambdaForm$DMH+0x000000010132c000'
Event: 104.449 Thread 0x000001edda8c1c80 Unloading class 0x000000010130cc00 'java/lang/invoke/LambdaForm$DMH+0x000000010130cc00'
Event: 104.449 Thread 0x000001edda8c1c80 Unloading class 0x000000010130c000 'java/lang/invoke/LambdaForm$DMH+0x000000010130c000'
Event: 104.449 Thread 0x000001edda8c1c80 Unloading class 0x00000001012b8400 'java/lang/invoke/LambdaForm$DMH+0x00000001012b8400'
Event: 104.449 Thread 0x000001edda8c1c80 Unloading class 0x000000010125c000 'java/lang/invoke/LambdaForm$DMH+0x000000010125c000'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 117.666 Thread 0x000001edeb5bab30 Exception <a 'sun/nio/fs/WindowsException'{0x000000008f406ce0}> (0x000000008f406ce0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 117.666 Thread 0x000001edeb5bab30 Exception <a 'sun/nio/fs/WindowsException'{0x000000008f4077b0}> (0x000000008f4077b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 117.666 Thread 0x000001edeb5bab30 Exception <a 'sun/nio/fs/WindowsException'{0x000000008f4082d8}> (0x000000008f4082d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 117.666 Thread 0x000001edeb5bab30 Exception <a 'sun/nio/fs/WindowsException'{0x000000008f412768}> (0x000000008f412768) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 120.576 Thread 0x000001ede59d73c0 Implicit null exception at 0x000001edcea40b8b to 0x000001edcea40c5c
Event: 2884.941 Thread 0x000001edeb5b76b0 Implicit null exception at 0x000001edcf2d2f16 to 0x000001edcf2d37ac
Event: 2885.721 Thread 0x000001edeb5b76b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000909f5640}> (0x00000000909f5640) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2885.722 Thread 0x000001edeb5b76b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000909f6b78}> (0x00000000909f6b78) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2885.722 Thread 0x000001edeb5b76b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000909f80a0}> (0x00000000909f80a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2885.723 Thread 0x000001edeb5b76b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000909f95a0}> (0x00000000909f95a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2886.649 Thread 0x000001edeb5b76b0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000092df1eb8}> (0x0000000092df1eb8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2886.656 Thread 0x000001edeb5b76b0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000092df3528}> (0x0000000092df3528) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2886.724 Thread 0x000001edeb5b76b0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000092df6938}> (0x0000000092df6938) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2887.933 Thread 0x000001edeb5b76b0 Implicit null exception at 0x000001edce69a0c0 to 0x000001edce69a12d
Event: 2890.009 Thread 0x000001edeb5b76b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000927b8f68}> (0x00000000927b8f68) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2890.009 Thread 0x000001edeb5b76b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000927ba600}> (0x00000000927ba600) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2890.009 Thread 0x000001edeb5b76b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000927bbce8}> (0x00000000927bbce8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2890.009 Thread 0x000001edeb5b76b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000927bd3d0}> (0x00000000927bd3d0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2890.179 Thread 0x000001edeb5bd920 Exception <a 'sun/nio/fs/WindowsException'{0x0000000091f670e8}> (0x0000000091f670e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 2892.970 Thread 0x000001edeb5c0710 Exception <a 'sun/nio/fs/WindowsException'{0x0000000090b41980}> (0x0000000090b41980) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 2894.059 Executing VM operation: Cleanup
Event: 2894.059 Executing VM operation: Cleanup done
Event: 2903.063 Executing VM operation: Cleanup
Event: 2903.063 Executing VM operation: Cleanup done
Event: 2904.063 Executing VM operation: Cleanup
Event: 2904.063 Executing VM operation: Cleanup done
Event: 2910.067 Executing VM operation: Cleanup
Event: 2910.067 Executing VM operation: Cleanup done
Event: 2924.073 Executing VM operation: Cleanup
Event: 2924.073 Executing VM operation: Cleanup done
Event: 2926.074 Executing VM operation: Cleanup
Event: 2926.074 Executing VM operation: Cleanup done
Event: 2927.074 Executing VM operation: Cleanup
Event: 2927.106 Executing VM operation: Cleanup done
Event: 2928.107 Executing VM operation: Cleanup
Event: 2928.124 Executing VM operation: Cleanup done
Event: 2929.124 Executing VM operation: Cleanup
Event: 2929.124 Executing VM operation: Cleanup done
Event: 2929.913 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 2930.048 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc87d8c10
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc8838810
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc8838d10
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc883d990
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc883e290
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc8841610
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc88d0c90
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc88da410
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc89f1610
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc89f2190
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc89f2910
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc89f2c10
Event: 113.633 Thread 0x000001edda8c1c80 flushing osr nmethod 0x000001edc89f4290
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc8a0e190
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc8a73890
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc8a9aa90
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc8af0990
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc8af0d90
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc8b1fc10
Event: 113.633 Thread 0x000001edda8c1c80 flushing  nmethod 0x000001edc8b6d090

Events (20 events):
Event: 2903.081 Thread 0x000001eddaa70650 Thread added: 0x000001ede46dcf90
Event: 2903.087 Thread 0x000001ede58d2960 Thread added: 0x000001ede58d0f20
Event: 2903.127 Thread 0x000001ede59d8e00 Thread added: 0x000001ede58d15b0
Event: 2908.127 Thread 0x000001ede46dcf90 Thread exited: 0x000001ede46dcf90
Event: 2923.669 Thread 0x000001ede59d8e00 Thread added: 0x000001edeb254ba0
Event: 2927.145 Thread 0x000001eddaa70650 Thread added: 0x000001ede46e13b0
Event: 2927.679 Thread 0x000001ede46e13b0 Thread exited: 0x000001ede46e13b0
Event: 2927.776 Thread 0x000001edeb5b7020 Thread added: 0x000001edeb253160
Event: 2927.776 Thread 0x000001edeb5b7020 Thread added: 0x000001edeb255f50
Event: 2927.776 Thread 0x000001edeb5b7020 Thread added: 0x000001edeb256c70
Event: 2927.893 Thread 0x000001eddaa70650 Thread added: 0x000001ede46d9fe0
Event: 2928.473 Thread 0x000001ede46d9fe0 Thread exited: 0x000001ede46d9fe0
Event: 2928.981 Thread 0x000001edeb5bdfb0 Thread added: 0x000001edeb257300
Event: 2928.982 Thread 0x000001edeb5bdfb0 Thread added: 0x000001edeb250370
Event: 2928.984 Thread 0x000001edeb5bdfb0 Thread added: 0x000001edeb257990
Event: 2928.999 Thread 0x000001eddaa70650 Thread added: 0x000001ede46de400
Event: 2929.382 Thread 0x000001ede46de400 Thread exited: 0x000001ede46de400
Event: 2929.580 Thread 0x000001eddaa70650 Thread added: 0x000001ede46dcf90
Event: 2930.104 Thread 0x000001ede58d50c0 Thread exited: 0x000001ede58d50c0
Event: 2930.104 Thread 0x000001ede58d0f20 Thread exited: 0x000001ede58d0f20


Dynamic libraries:
0x00007ff6d7e10000 - 0x00007ff6d7e1a000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\java.exe
0x00007ffe70210000 - 0x00007ffe70408000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffe6fe00000 - 0x00007ffe6febf000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffe6db30000 - 0x00007ffe6de26000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffe6d8f0000 - 0x00007ffe6d9f0000 	C:\Windows\System32\ucrtbase.dll
0x00007ffe1d2d0000 - 0x00007ffe1d2e8000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\jli.dll
0x00007ffe1d970000 - 0x00007ffe1d98b000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffe6f8d0000 - 0x00007ffe6fa6d000 	C:\Windows\System32\USER32.dll
0x00007ffe6e240000 - 0x00007ffe6e262000 	C:\Windows\System32\win32u.dll
0x00007ffe6f390000 - 0x00007ffe6f3bc000 	C:\Windows\System32\GDI32.dll
0x00007ffe59990000 - 0x00007ffe59c2a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e\COMCTL32.dll
0x00007ffe6e030000 - 0x00007ffe6e145000 	C:\Windows\System32\gdi32full.dll
0x00007ffe6fba0000 - 0x00007ffe6fc3e000 	C:\Windows\System32\msvcrt.dll
0x00007ffe6df90000 - 0x00007ffe6e02d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffe6e300000 - 0x00007ffe6e330000 	C:\Windows\System32\IMM32.DLL
0x00007ffe69230000 - 0x00007ffe6923c000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffe59840000 - 0x00007ffe598cd000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\msvcp140.dll
0x00007ffe1c0e0000 - 0x00007ffe1cd6a000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\server\jvm.dll
0x00007ffe6f2e0000 - 0x00007ffe6f38f000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffe6ffa0000 - 0x00007ffe7003c000 	C:\Windows\System32\sechost.dll
0x00007ffe6fcb0000 - 0x00007ffe6fdd6000 	C:\Windows\System32\RPCRT4.dll
0x00007ffe6f720000 - 0x00007ffe6f78b000 	C:\Windows\System32\WS2_32.dll
0x00007ffe6d0b0000 - 0x00007ffe6d0fb000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffe4c150000 - 0x00007ffe4c177000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffe67090000 - 0x00007ffe6709a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffe6d090000 - 0x00007ffe6d0a2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffe6b730000 - 0x00007ffe6b742000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffe52170000 - 0x00007ffe5217a000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\jimage.dll
0x00007ffe53bf0000 - 0x00007ffe53dd4000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffe5a7e0000 - 0x00007ffe5a814000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffe6d9f0000 - 0x00007ffe6da72000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffe51390000 - 0x00007ffe5139e000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\instrument.dll
0x00007ffe1d2b0000 - 0x00007ffe1d2d0000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\java.dll
0x00007ffe1d290000 - 0x00007ffe1d2a8000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\zip.dll
0x00007ffe6eb30000 - 0x00007ffe6f274000 	C:\Windows\System32\SHELL32.dll
0x00007ffe6b930000 - 0x00007ffe6c0c3000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffe6f3c0000 - 0x00007ffe6f714000 	C:\Windows\System32\combase.dll
0x00007ffe6d2d0000 - 0x00007ffe6d2fe000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffe70120000 - 0x00007ffe701cd000 	C:\Windows\System32\SHCORE.dll
0x00007ffe6f280000 - 0x00007ffe6f2d5000 	C:\Windows\System32\shlwapi.dll
0x00007ffe6d7c0000 - 0x00007ffe6d7df000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffe4fa00000 - 0x00007ffe4fa10000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\net.dll
0x00007ffe64ac0000 - 0x00007ffe64bca000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffe6cfc0000 - 0x00007ffe6d02a000 	C:\Windows\system32\mswsock.dll
0x00007ffe1d270000 - 0x00007ffe1d286000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\nio.dll
0x00007ffe4ace0000 - 0x00007ffe4acf0000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\verify.dll
0x00007ffe5d2c0000 - 0x00007ffe5d2e7000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffe596f0000 - 0x00007ffe59834000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ffe4ce00000 - 0x00007ffe4ce09000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\management.dll
0x00007ffe4cb20000 - 0x00007ffe4cb2b000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\management_ext.dll
0x00007ffe6f8c0000 - 0x00007ffe6f8c8000 	C:\Windows\System32\PSAPI.DLL
0x00007ffe6d230000 - 0x00007ffe6d248000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffe6c870000 - 0x00007ffe6c8a4000 	C:\Windows\system32\rsaenh.dll
0x00007ffe6e210000 - 0x00007ffe6e237000 	C:\Windows\System32\bcrypt.dll
0x00007ffe6d780000 - 0x00007ffe6d7ae000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffe6d220000 - 0x00007ffe6d22c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffe6ccb0000 - 0x00007ffe6ccec000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffe6fc40000 - 0x00007ffe6fc48000 	C:\Windows\System32\NSI.dll
0x00007ffe4c280000 - 0x00007ffe4c289000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\extnet.dll
0x00007ffe553e0000 - 0x00007ffe553e7000 	C:\Windows\system32\wshunix.dll
0x00007ffe6ccf0000 - 0x00007ffe6cdba000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffe58610000 - 0x00007ffe5861a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffe58060000 - 0x00007ffe580e2000 	C:\Windows\System32\fwpuclnt.dll
0x00007ffe2dd60000 - 0x00007ffe2dd77000 	C:\Windows\system32\napinsp.dll
0x00007ffe2dd40000 - 0x00007ffe2dd5b000 	C:\Windows\system32\pnrpnsp.dll
0x00007ffe67d70000 - 0x00007ffe67d85000 	C:\Windows\system32\wshbth.dll
0x00007ffe690d0000 - 0x00007ffe690ed000 	C:\Windows\system32\NLAapi.dll
0x00007ffe2dd20000 - 0x00007ffe2dd32000 	C:\Windows\System32\winrnr.dll
0x00007ffe673e0000 - 0x00007ffe673e7000 	C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\rmi.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e;C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\gradle-daemon-main-8.11.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Program Files\Common Files\Oracle\Java\javapath;C:\Python312\Scripts\;C:\Python312\;C:\Program Files\PerkinElmerInformatics\ChemOffice2020\ChemScript\Lib;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\gsudo\Current;C:\ProgramData\chocolatey\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;C:\Pro;ram Files\nodejs\;C:\Program Files\nodejs\;C:\Program Files\Go\bin;D:\UserData\Downloads\flutter_windows_3.16.8-stable\flutter\bin;C:\Program Files\Git\cmd;E:\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\go\bin;E:\PyCharm 2024.1\bin;E:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 30, weak refs: 14

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 567312K (6% of 8275224K total physical memory with 716572K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 54198K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 45303K
Loader bootstrap                                                                       : 35160K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 14012K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 836K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 692K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 252K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 237K
Loader sun.reflect.misc.MethodUtil                                                     : 2952B

Classes loaded by more than one classloader:
Class Program                                                                         : loaded 5 times (x 68B)
Class Build_gradle                                                                    : loaded 3 times (x 126B)
Class Build_gradle$1                                                                  : loaded 3 times (x 70B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 2 times (x 107B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 2 times (x 142B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 2 times (x 93B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 2 times (x 117B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 2 times (x 121B)
Class com.google.common.reflect.TypeCapture                                           : loaded 2 times (x 67B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 65B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 84B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 2 times (x 91B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 71B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 65B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class [Lcom.google.common.collect.AbstractIterator$State;                             : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 2 times (x 65B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class com.google.common.collect.AbstractIterator$1                                    : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableRangeSet                                     : loaded 2 times (x 111B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 121B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSet$SerializedForm                           : loaded 2 times (x 69B)
Class com.google.common.collect.Sets$1                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 74B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 68B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class com.google.common.collect.Maps$ViewCachingAbstractMap                           : loaded 2 times (x 121B)
Class [Lcom.google.common.base.Function;                                              : loaded 2 times (x 65B)
Class com.google.common.collect.Sets$2                                                : loaded 2 times (x 135B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class com.google.common.reflect.Types$ClassOwnership$1                                : loaded 2 times (x 76B)
Class [Lorg.objectweb.asm.AnnotationVisitor;                                          : loaded 2 times (x 65B)
Class com.google.common.collect.Sets$3                                                : loaded 2 times (x 135B)
Class com.google.common.util.concurrent.AbstractFuture$SafeAtomicHelper               : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 130B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 75B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class com.google.common.reflect.Types$ClassOwnership$2                                : loaded 2 times (x 76B)
Class com.google.common.reflect.TypeResolver                                          : loaded 2 times (x 68B)
Class com.google.common.collect.Sets$4                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 77B)
Class com.google.common.reflect.Types$ClassOwnership$3                                : loaded 2 times (x 67B)
Class com.google.common.collect.Collections2$FilteredCollection                       : loaded 2 times (x 115B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 77B)
Class com.google.common.collect.TransformedIterator                                   : loaded 2 times (x 76B)
Class [Lcom.google.common.collect.Maps$EntryFunction;                                 : loaded 2 times (x 65B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 77B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 73B)
Class com.google.common.reflect.Invokable                                             : loaded 2 times (x 99B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 74B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 78B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class org.gradle.internal.classloader.InstrumentingClassLoader                        : loaded 2 times (x 66B)
Class com.google.common.reflect.Types$ParameterizedTypeImpl                           : loaded 2 times (x 77B)
Class com.google.common.collect.Maps$IteratorBasedAbstractMap$1                       : loaded 2 times (x 134B)
Class com.google.common.collect.ImmutableRangeSet$AsSet                               : loaded 2 times (x 295B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 71B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 2 times (x 93B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 2 times (x 72B)
Class com.google.common.reflect.TypeResolver$TypeVariableKey                          : loaded 2 times (x 68B)
Class com.google.common.base.Joiner                                                   : loaded 2 times (x 75B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 67B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 65B)
Class com.google.common.collect.Platform                                              : loaded 2 times (x 67B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 2 times (x 76B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class com.google.common.collect.HashBiMap$BiEntry                                     : loaded 2 times (x 79B)
Class com.google.common.base.Ascii                                                    : loaded 2 times (x 67B)
Class com.google.common.collect.SingletonImmutableBiMap                               : loaded 2 times (x 139B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 85B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 2 times (x 77B)
Class com.google.common.reflect.TypeToken$SimpleTypeToken                             : loaded 2 times (x 69B)
Class com.google.common.collect.Sets$ImprovedAbstractSet                              : loaded 2 times (x 131B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 2 times (x 73B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 138B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 2 times (x 119B)
Class com.google.common.collect.ImmutableRangeSet$1                                   : loaded 2 times (x 205B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper$1           : loaded 2 times (x 72B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 2 times (x 212B)
Class com.google.common.base.Optional                                                 : loaded 2 times (x 76B)
Class com.google.common.collect.Range                                                 : loaded 2 times (x 83B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 66B)
Class com.google.common.collect.Iterators                                             : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$RangesMatcher                                : loaded 2 times (x 108B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 78B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 2 times (x 203B)
Class org.gradle.kotlin.dsl.VersionCatalogAccessorsKt                                 : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableSortedSetFauxverideShim                      : loaded 2 times (x 143B)
Class com.google.common.base.CharMatcher$JavaLetterOrDigit                            : loaded 2 times (x 107B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet$1                     : loaded 2 times (x 78B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 102B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 78B)
Class com.google.common.base.CharMatcher$And                                          : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class com.google.common.collect.FluentIterable$1                                      : loaded 2 times (x 77B)
Class com.google.common.base.Objects                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 73B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 2 times (x 146B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 2 times (x 77B)
Class com.google.common.collect.BiMap                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class com.google.common.reflect.Reflection                                            : loaded 2 times (x 67B)
Class com.google.common.collect.FluentIterable$2                                      : loaded 2 times (x 77B)
Class com.google.common.base.Absent                                                   : loaded 2 times (x 76B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIterator                                      : loaded 2 times (x 78B)
Class com.google.common.collect.FluentIterable$3                                      : loaded 2 times (x 77B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 2 times (x 146B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 168B)
Class com.google.common.collect.ObjectArrays                                          : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 2 times (x 92B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 2 times (x 147B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 77B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 2 times (x 78B)
Class com.google.common.base.NullnessCasts                                            : loaded 2 times (x 67B)
Class com.google.common.collect.Maps$EntryFunction$1                                  : loaded 2 times (x 87B)
Class com.google.common.base.Present                                                  : loaded 2 times (x 77B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 66B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class com.google.common.reflect.Invokable$ConstructorInvokable                        : loaded 2 times (x 103B)
Class com.google.common.collect.Maps$EntryFunction$2                                  : loaded 2 times (x 87B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Multimap                                              : loaded 2 times (x 66B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class com.google.common.reflect.TypeToken                                             : loaded 2 times (x 69B)
Class com.google.common.base.ExtraObjectsMethodsForWeb                                : loaded 2 times (x 67B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 78B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 68B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 2 times (x 203B)
Class Settings_gradle$1                                                               : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 78B)
Class [Lcom.google.common.reflect.Types$JavaVersion;                                  : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 2 times (x 204B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 71B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 70B)
Class com.google.common.util.concurrent.AbstractFuture$AtomicHelper                   : loaded 2 times (x 74B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 68B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 75B)
Class com.google.common.collect.Iterators$1                                           : loaded 2 times (x 77B)
Class com.google.common.reflect.TypeToken$1                                           : loaded 2 times (x 106B)
Class com.google.common.cache.LocalCache$ComputingValueReference                      : loaded 2 times (x 92B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 2 times (x 142B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 2 times (x 121B)
Class com.google.common.reflect.TypeToken$2                                           : loaded 2 times (x 106B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 150B)
Class com.google.common.collect.SortedIterable                                        : loaded 2 times (x 66B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 65B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 70B)
Class com.google.common.collect.Iterators$4                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 2 times (x 139B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 168B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 68B)
Class com.google.common.collect.Iterators$5                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 2 times (x 72B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 80B)
Class com.google.common.collect.Iterators$6                                           : loaded 2 times (x 77B)
Class com.google.common.collect.HashBiMap$Inverse                                     : loaded 2 times (x 138B)
Class com.google.common.io.Closer$SuppressingSuppressor                               : loaded 2 times (x 71B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper             : loaded 2 times (x 74B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 71B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 2 times (x 108B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 2 times (x 121B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 2 times (x 108B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 82B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 66B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSet$CachingAsList                            : loaded 2 times (x 143B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 86B)
Class com.google.common.collect.Lists                                                 : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$Any                                          : loaded 2 times (x 108B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 102B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 79B)
Class com.google.common.collect.Iterators$9                                           : loaded 2 times (x 77B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 135B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 2 times (x 205B)
Class com.google.common.reflect.TypeResolver$TypeTable                                : loaded 2 times (x 69B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 73B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 71B)
Class com.google.common.reflect.TypeResolver$TypeTable$1                              : loaded 2 times (x 69B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 66B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class com.google.common.collect.Lists$ReverseList                                     : loaded 2 times (x 196B)
Class com.google.common.collect.ImmutableSortedSet                                    : loaded 2 times (x 295B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$JavaLetter                                   : loaded 2 times (x 107B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 95B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 2 times (x 107B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 70B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet                       : loaded 2 times (x 133B)
Class com.google.common.collect.RegularImmutableSortedSet                             : loaded 2 times (x 295B)
Class com.google.common.math.IntMath                                                  : loaded 2 times (x 67B)
Class com.google.common.reflect.Types$TypeVariableInvocationHandler                   : loaded 2 times (x 71B)
Class [Lcom.google.common.collect.HashBiMap$BiEntry;                                  : loaded 2 times (x 65B)
Class com.google.common.collect.Iterators$10                                          : loaded 2 times (x 77B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 2 times (x 81B)
Class com.google.common.base.CharMatcher$JavaDigit                                    : loaded 2 times (x 107B)
Class com.google.common.base.Predicates                                               : loaded 2 times (x 67B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 2 times (x 81B)
Class com.google.common.base.Function                                                 : loaded 2 times (x 66B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 78B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 2 times (x 208B)
Class com.google.common.reflect.Types$ClassOwnership$1LocalClass                      : loaded 2 times (x 67B)
Class com.google.common.collect.Maps$EntryFunction                                    : loaded 2 times (x 87B)
Class com.google.common.collect.ImmutableBiMapFauxverideShim                          : loaded 2 times (x 116B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class com.google.common.base.Charsets                                                 : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 67B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$JavaUpperCase                                : loaded 2 times (x 107B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class com.google.common.reflect.Types$JavaVersion                                     : loaded 2 times (x 79B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 2 times (x 65B)
Class com.google.common.collect.Sets$3$1                                              : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 92B)
Class com.google.common.math.MathPreconditions                                        : loaded 2 times (x 67B)
Class com.google.common.reflect.Invokable$MethodInvokable                             : loaded 2 times (x 103B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 67B)
Class [Lorg.objectweb.asm.AnnotationWriter;                                           : loaded 2 times (x 65B)
Class com.google.common.primitives.Ints$IntConverter                                  : loaded 2 times (x 86B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 78B)
Class com.google.common.base.Predicates$CompositionPredicate                          : loaded 2 times (x 84B)
Class com.google.common.collect.Lists$ReverseList$1                                   : loaded 2 times (x 95B)
Class com.google.common.collect.ImmutableRangeSet$Builder                             : loaded 2 times (x 73B)
Class com.google.common.collect.Sets$SetView                                          : loaded 2 times (x 134B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 86B)
Class com.google.common.util.concurrent.AbstractFuture$SynchronizedHelper             : loaded 2 times (x 74B)
Class com.google.common.base.CharMatcher$Invisible                                    : loaded 2 times (x 108B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 75B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 65B)
Class com.google.common.math.IntMath$1                                                : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class com.google.common.reflect.Types$ClassOwnership                                  : loaded 2 times (x 76B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 69B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 76B)
Class com.google.common.collect.Sets$FilteredSet                                      : loaded 2 times (x 133B)
Class com.google.common.io.Closer                                                     : loaded 2 times (x 74B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 2 times (x 203B)
Class com.google.common.collect.ImmutableSet                                          : loaded 2 times (x 141B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 2 times (x 108B)
Class com.google.common.base.CharMatcher$Ascii                                        : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 75B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 67B)
Class com.google.common.io.Closer$Suppressor                                          : loaded 2 times (x 66B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 74B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 2 times (x 108B)
Class Settings_gradle$1$1                                                             : loaded 2 times (x 70B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 73B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class com.google.common.collect.ImmutableList$SerializedForm                          : loaded 2 times (x 69B)
Class com.google.common.util.concurrent.AbstractFuture$Waiter                         : loaded 2 times (x 68B)
Class Settings_gradle                                                                 : loaded 2 times (x 124B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 67B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 66B)
Class com.google.common.collect.HashBiMap                                             : loaded 2 times (x 139B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Sets                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 68B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 2 times (x 107B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 65B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableMap                                          : loaded 2 times (x 116B)
Class com.google.common.base.Predicate                                                : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class com.google.common.collect.AbstractRangeSet                                      : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 183B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 2 times (x 109B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 2 times (x 78B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 168B)
Class com.google.common.base.AbstractIterator$1                                       : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$Digit                                        : loaded 2 times (x 108B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 78B)
Class com.google.common.base.Suppliers$MemoizingSupplier                              : loaded 2 times (x 75B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$JavaIsoControl                               : loaded 2 times (x 108B)
Class com.google.common.collect.Maps$AbstractFilteredMap                              : loaded 2 times (x 123B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$JavaLowerCase                                : loaded 2 times (x 107B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 79B)
Class com.google.common.reflect.Types$NativeTypeVariableEquals                        : loaded 2 times (x 67B)
Class com.google.common.reflect.TypeParameter                                         : loaded 2 times (x 68B)
Class com.google.common.collect.Iterables$5                                           : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 103B)
Class com.google.common.base.Splitter$1$1                                             : loaded 2 times (x 82B)
Class com.google.common.base.CharMatcher$Whitespace                                   : loaded 2 times (x 108B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableRangeSet$ComplementRanges                    : loaded 2 times (x 203B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 68B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 2 times (x 73B)
Class com.google.common.base.CharMatcher$SingleWidth                                  : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class com.google.common.collect.FluentIterable                                        : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 104B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 110B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 66B)
Class com.google.common.base.Suppliers$NonSerializableMemoizingSupplier               : loaded 2 times (x 75B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 102B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 65B)
Class com.google.common.collect.RegularImmutableMap$BucketOverflowException           : loaded 2 times (x 78B)
Class com.google.common.reflect.Types$JavaVersion$1                                   : loaded 2 times (x 79B)
Class com.google.common.collect.AbstractIterator$State                                : loaded 2 times (x 75B)
Class com.google.common.collect.RegularImmutableBiMap                                 : loaded 2 times (x 144B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 2 times (x 107B)
Class com.google.common.reflect.Types$JavaVersion$2                                   : loaded 2 times (x 79B)
Class com.google.common.reflect.Types                                                 : loaded 2 times (x 67B)
Class com.google.common.hash.PrimitiveSink                                            : loaded 2 times (x 66B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 68B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 78B)
Class com.google.common.collect.Hashing                                               : loaded 2 times (x 67B)
Class com.google.common.base.Preconditions                                            : loaded 2 times (x 67B)
Class com.google.common.reflect.Types$JavaVersion$3                                   : loaded 2 times (x 79B)
Class com.google.common.base.Joiner$1                                                 : loaded 2 times (x 76B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 69B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 79B)
Class com.google.common.collect.PeekingIterator                                       : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher                                              : loaded 2 times (x 107B)
Class com.google.common.reflect.Types$JavaVersion$4                                   : loaded 2 times (x 79B)
Class com.google.common.collect.Maps$IteratorBasedAbstractMap                         : loaded 2 times (x 121B)
Class com.google.common.collect.RangeGwtSerializationDependencies                     : loaded 2 times (x 67B)
Class com.google.common.base.Joiner$2                                                 : loaded 2 times (x 75B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 101B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$1                                            : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 2 times (x 108B)
Class com.google.common.reflect.Types$JavaVersion$5                                   : loaded 2 times (x 67B)
Class [Lcom.google.common.reflect.Types$ClassOwnership;                               : loaded 2 times (x 65B)
Class com.google.common.collect.RangeSet                                              : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 79B)
Class org.gradle.api.internal.classpath.GlobalCacheRootsProvider                      : loaded 2 times (x 66B)
Class com.google.common.collect.CollectCollectors                                     : loaded 2 times (x 67B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 79B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 2 times (x 147B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 86B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 2 times (x 81B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 2 times (x 107B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 79B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 79B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 79B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class com.google.common.collect.Maps$KeySet                                           : loaded 2 times (x 133B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$EmptySetBuilderImpl                      : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableList                                         : loaded 2 times (x 202B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class com.google.common.collect.Maps$EntrySet                                         : loaded 2 times (x 132B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 2 times (x 72B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 71B)
Class com.google.common.collect.ForwardingObject                                      : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 2 times (x 204B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$SetFuture                      : loaded 2 times (x 71B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 66B)
Class com.google.common.reflect.Types$TypeVariableImpl                                : loaded 2 times (x 68B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 2 times (x 77B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 2 times (x 144B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)
Class com.google.common.collect.Iterables                                             : loaded 2 times (x 67B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 66B)
Class com.google.common.collect.Lists$RandomAccessReverseList                         : loaded 2 times (x 196B)

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.3031)
OS uptime: 0 days 21:42 hours

CPU: total 4 (initial active 4) (4 cores per cpu, 1 threads per core) family 6 model 158 stepping 9 microcode 0xb4, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 4 processors :
  Max Mhz: 3001, Current Mhz: 3001, Mhz Limit: 3001

Memory: 4k page, system-wide physical 8081M (708M free)
TotalPageFile size 19857M (AvailPageFile size 7318M)
current process WorkingSet (physical memory assigned to process): 551M, peak: 574M
current process commit charge ("private bytes"): 752M, peak: 762M

vm_info: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29) for windows-amd64 JRE (21.0.5+-13047016-b750.29), built on 2025-02-11T21:12:39Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
