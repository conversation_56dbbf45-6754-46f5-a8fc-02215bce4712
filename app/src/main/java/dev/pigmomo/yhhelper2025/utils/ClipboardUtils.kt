package dev.pigmomo.yhhelper2025.utils

import androidx.compose.ui.platform.ClipboardManager
import androidx.compose.ui.text.AnnotatedString
import dev.pigmomo.yhhelper2025.data.model.TokenEntity

/**
 * 剪贴板工具类 - 处理剪贴板相关操作
 */
object ClipboardUtils {
    
    /**
     * 复制令牌信息到剪贴板
     *
     * @param clipboardManager 剪贴板管理器
     * @param token 要复制的令牌
     */
    fun copyToken(clipboardManager: ClipboardManager, token: TokenEntity) {
        clipboardManager.setText(
            AnnotatedString(
                listOfNotNull(
                    token.phoneNumber,
                    token.uid,
                    token.userKey + "-601933-" + token.accessToken,
                    token.refreshToken,
                    token.appParam,
                    token.updateDate + " " + token.extraNote,
                ).joinToString("----")
            )
        )
    }

    /**
     * 检查剪贴板文本类型
     *
     * @param clipboardText 剪贴板文本
     * @return 文本类型描述
     */
    fun checkClipboardTextType(clipboardText: String): String {
        if (clipboardText.contains("prizeId") && clipboardText.contains("gameCode")) {
            return "助力券"
        }

        if (clipboardText.contains("eventId")) {
            return "拼手气"
        }

        if (clipboardText.contains("teamCode")) {
            return "组队瓜分"
        }

        if (clipboardText.contains("----") && clipboardText.split("----").size > 4) {
            return "INPUT TOKEN"
        }

        return ""
    }
}