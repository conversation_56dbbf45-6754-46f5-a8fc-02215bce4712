package dev.pigmomo.yhhelper2025.utils

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.io.IOException
import java.net.SocketTimeoutException
import java.util.concurrent.TimeUnit

/**
 * OkHttp工具类
 */
object OkHttpUtils {
    private const val TAG = "OkHttpUtils"
    private const val LOCAL_SERVER_URL = "http://localhost:8202"
    private const val TIMEOUT = 10L // 超时时间，单位秒

    // 创建OkHttpClient实例
    private val client = OkHttpClient.Builder()
        .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
        .readTimeout(TIMEOUT, TimeUnit.SECONDS)
        .writeTimeout(TIMEOUT, TimeUnit.SECONDS)
        .build()

    /**
     * 从本地HTTP服务获取数据
     * @return 成功返回响应文本，失败返回空字符串
     */
    suspend fun fetchDataFromLocalServer(): String = withContext(Dispatchers.IO) {
        val request = Request.Builder()
            .url("$LOCAL_SERVER_URL/data")
            .build()

        return@withContext executeRequest(request)
    }
    
    /**
     * 发送POST请求到本地服务器
     * @param path 请求路径
     * @param jsonBody JSON请求体
     * @return 成功返回响应文本，失败返回空字符串
     */
    suspend fun postJsonData(path: String, jsonBody: String): String = withContext(Dispatchers.IO) {
        val mediaType = "application/json; charset=utf-8".toMediaType()
        val requestBody = jsonBody.toRequestBody(mediaType)
        
        val request = Request.Builder()
            .url("$LOCAL_SERVER_URL/$path")
            .post(requestBody)
            .build()
            
        return@withContext executeRequest(request)
    }

    /**
     * 检查本地HTTP服务是否运行
     * @return 服务运行状态
     */
    suspend fun isLocalServerRunning(): Boolean = withContext(Dispatchers.IO) {
        val request = Request.Builder()
            .url(LOCAL_SERVER_URL)
            .build()

        return@withContext try {
            client.newCall(request).execute().use { response ->
                response.isSuccessful
            }
        } catch (e: Exception) {
            logError("failed to check local server status", e)
            false
        }
    }
    
    /**
     * 执行HTTP请求
     * @param request OkHttp请求对象
     * @return 成功返回响应文本，失败返回空字符串
     */
    private suspend fun executeRequest(request: Request): String = withContext(Dispatchers.IO) {
        return@withContext try {
            client.newCall(request).execute().use { response ->
                if (response.isSuccessful) {
                    val responseBody = response.body?.string() ?: ""
                    Log.d(TAG, "HTTP request successful: ${request.url}")
                    responseBody
                } else {
                    Log.e(TAG, "HTTP request failed: HTTP ${response.code} - ${request.url}")
                    ""
                }
            }
        } catch (e: SocketTimeoutException) {
            logError("HTTP request timeout: ${request.url}", e)
            ""
        } catch (e: IOException) {
            logError("HTTP request IO exception: ${request.url}", e)
            ""
        } catch (e: Exception) {
            logError("HTTP request exception: ${request.url}", e)
            ""
        }
    }
    
    /**
     * 记录错误日志
     */
    private fun logError(message: String, e: Exception) {
        val errorDetails = when (e) {
            is SocketTimeoutException -> "Connection timeout"
            is IOException -> "Network IO error: ${e.message}"
            else -> e.message ?: "Unknown error"
        }
        Log.e(TAG, "$message: $errorDetails")
    }
} 