package dev.pigmomo.yhhelper2025.utils

import java.util.UUID
import kotlin.random.Random

/**
 * Web相关工具类
 */
object WebUtils {

    /**
     * 生成随机的会话ID
     * @return 随机生成的会话ID字符串
     */
    fun generateRandomJySessionId(): String {
        val uuid = UUID.randomUUID().toString().replace("-", "")
        val random = Random.nextInt(100000, 999999)
        return "$uuid$random"
    }

    /**
     * 根据设备参数生成UserAgent字符串
     * @param appParam 应用参数字符串，格式为逗号分隔的多个参数
     * @return 定制的UserAgent字符串
     */
    fun generateUserAgent(appParam: String): String {
        val params = appParam.split(",")
        // 如果参数不足，返回默认UserAgent
        if (params.size < 9) {
            return "Mozilla/5.0 (Linux; Android 13; 23013RK75C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Mobile Safari/537.36 EdgA/114.0.1823.43"
        }
        
        //android33
        val osParam = params[4]
        // 安全地获取Android版本号，避免索引越界
        val osVersion = if (osParam.contains("android")) {
            val parts = osParam.split("android")
            if (parts.size > 1 && parts[1].isNotEmpty()) parts[1] else "12"
        } else {
            "12" // 默认使用Android 12作为回退版本
        }
        val deviceModel = params[7] // 设备型号
        
        return "Mozilla/5.0 (Linux; Android $osVersion; $deviceModel) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Mobile Safari/537.36 EdgA/114.0.1823.43"
    }
} 