package dev.pigmomo.yhhelper2025.utils

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import androidx.core.app.NotificationCompat
import dev.pigmomo.yhhelper2025.MainActivity
import dev.pigmomo.yhhelper2025.R

/**
 * 通知工具类 - 处理通知相关操作
 */
object NotificationUtils {
    private const val CHANNEL_ID = "default_channel"
    private const val CHANNEL_NAME = "Default Channel"
    private const val NOTIFICATION_ID = 1000
    
    /**
     * 创建通知渠道
     *
     * @param context 上下文
     */
    fun createNotificationChannel(context: Context) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val channel = NotificationChannel(
            CHANNEL_ID,
            CHANNEL_NAME,
            NotificationManager.IMPORTANCE_DEFAULT
        )
        notificationManager.createNotificationChannel(channel)
    }
    
    /**
     * 显示持久通知
     *
     * @param context 上下文
     */
    fun showNotification(context: Context) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setContentTitle("永助后台运行中")
            .setContentText("回到应用")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build()
            
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
}