package dev.pigmomo.yhhelper2025.utils

import android.content.ContentValues
import android.content.Context
import de.robv.android.xposed.XC_MethodHook
import de.robv.android.xposed.XposedBridge
import org.xmlpull.v1.XmlPullParser
import org.xmlpull.v1.XmlPullParserFactory
import java.io.StringReader
import java.net.URLDecoder
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import androidx.core.content.edit
import dev.pigmomo.yhhelper2025.data.model.LinkData
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import io.ktor.server.engine.*
import io.ktor.server.netty.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.request.*
import org.json.JSONArray
import org.json.JSONObject

private val YONGHUI_APPID = listOf("wx194053ab36997e52", "wxc9cf7c95499ee604")
private val TARGET_CHATROOM = listOf("***********@chatroom", "***********@chatroom")
private const val PREF_NAME = "yhhelper_links"
private const val KEY_LINKS = "links"

class MessageUtils {
    private var context: Context? = null
    private val gson = Gson()
    private var sharedPreferences: SharedPreferences? = null
    private var httpServer: ApplicationEngine? = null

    /**
     * 初始化上下文
     * @param context 微信应用上下文
     */
    fun initContext(context: Context) {
        this.context = context
        initSharedPreferences(context)

        launchHttpServer()
    }

    /**
     * 初始化SharedPreferences
     * @param context 微信应用上下文
     */
    private fun initSharedPreferences(context: Context) {
        try {
            sharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)

            XposedBridge.log("yhhelper2025: SharedPreferences initialized successfully")
        } catch (e: Exception) {
            XposedBridge.log("yhhelper2025: failed to initialize SharedPreferences: ${e.message}")
        }
    }

    fun messageDeal(
        param: XC_MethodHook.MethodHookParam
    ) {
        val insertType = param.args[0] as String
        val contentValues = param.args[2] as ContentValues

        //XposedBridge.log("yhhelper2025: insertType: $insertType")

        // 只处理消息表的数据
        if (insertType != "message") {
            return
        }

        val createTime = contentValues.getAsLong("createTime")
        val content = contentValues.getAsString("content") ?: return
        val talker = contentValues.getAsString("talker") ?: return

        // 只处理接收到的XML格式消息
        if (!content.contains("<msg>")) {
            return
        }

        try {
            // 预处理消息内容，移除可能导致XML解析错误的前缀
            val processedContent = preprocessContent(content)

            // 尝试从不同位置提取 appId
            var appId = extractAppIdFromXmlAppmsgAppid(processedContent)
            if (appId?.isEmpty() == true) {
                appId = extractAppIdFromXmlAppid(processedContent)
            }
            if (appId?.isEmpty() == true) {
                appId = extractAppIdFromWeappinfo(processedContent)
            }

            XposedBridge.log("yhhelper2025: appId: $appId")

            if (appId in YONGHUI_APPID) {
                // 尝试从不同位置提取 pagePath
                var pagePath = extractPagePathFromXml(processedContent)
                if (pagePath.isNullOrEmpty()) {
                    pagePath = extractPagePathFromWeappinfo(processedContent)
                }
                if (pagePath.isNullOrEmpty()) {
                    XposedBridge.log("yhhelper2025: pagePath is null or empty")
                    return
                }

                val decodedUrl = URLDecoder.decode(pagePath, "UTF-8")

                XposedBridge.log("yhhelper2025: found link: $decodedUrl")

                // 限制群聊
                if (talker.contains("@chatroom") && talker !in TARGET_CHATROOM) {
                    XposedBridge.log("yhhelper2025: not target group message, skipping")
                    return
                }

                // 保存链接到SharedPreferences
                saveLinkToSharedPreferences(createTime, decodedUrl)
            }
        } catch (e: Exception) {
            XposedBridge.log(
                "yhhelper2025: error processing message: ${e.message}, content format: ${
                    content.take(
                        50
                    )
                }..."
            )
        }
    }

    /**
     * 预处理消息内容，移除可能导致XML解析错误的前缀
     * @param content 原始消息内容
     * @return 处理后的消息内容
     */
    private fun preprocessContent(content: String): String {
        // 如果消息内容包含冒号，则截取第一个冒号后的内容
        if (content.contains(":")) {
            val colonIndex = content.indexOf(":")
            if (colonIndex > 0 && colonIndex + 1 < content.length) {
                val processed = content.substring(colonIndex + 1).trim()
                // 确保处理后的内容仍然包含XML
                if (processed.contains("<msg>")) {
                    XposedBridge.log("yhhelper2025: preprocessed content, removed prefix before first colon")
                    return processed
                }
            }
        }
        return content
    }

    /**
     * 保存链接数据到SharedPreferences
     * @param createTime 消息创建时间
     * @param url 解析后的URL
     */
    private fun saveLinkToSharedPreferences(createTime: Long, url: String) {
        if (sharedPreferences != null) {
            try {

                val keyParam = LinkData.getKeyParam(url)
                if (keyParam.isEmpty()) {
                    XposedBridge.log("yhhelper2025: keyParam is empty, cannot save link")
                    return
                }

                val newLink = LinkData(createTime, keyParam)

                val linksJson = sharedPreferences?.getString(KEY_LINKS, "")
                val linksList = if (linksJson.isNullOrEmpty()) {
                    mutableListOf<LinkData>()
                } else {
                    val type = object : TypeToken<MutableList<LinkData>>() {}.type
                    gson.fromJson<MutableList<LinkData>>(linksJson, type)
                }

                // 检查是否已存在相同keyParam的链接
                val exists = linksList.any { it.keyParam == keyParam }
                if (!exists) {
                    linksList.add(newLink)

                    // 如果链接数量超过200，删除最旧的链接（按createTime排序）
                    if (linksList.size > 200) {
                        linksList.sortBy { it.createTime }
                        linksList.removeAt(0) // 移除最旧的一条
                        XposedBridge.log("yhhelper2025: removed oldest link, list size exceeds 100")
                    }

                    sharedPreferences?.edit() {
                        this.putString(KEY_LINKS, gson.toJson(linksList))
                    }

                    XposedBridge.log("yhhelper2025: saved link to SharedPreferences: $url")
                } else {
                    XposedBridge.log("yhhelper2025: link with keyParam $keyParam already exists, skipping")
                }
            } catch (e: Exception) {
                XposedBridge.log("yhhelper2025: error saving to SharedPreferences: ${e.message}")
            }
        } else {
            XposedBridge.log("yhhelper2025: wechatContext is null, cannot save link")
        }
    }

    private fun launchHttpServer() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                if (httpServer == null) {
                    httpServer = embeddedServer(Netty, port = 8202, host = "0.0.0.0") {
                        routing {
                            get("/") {
                                call.respondText("http server is running")
                            }

                            get("/data") {
                                sharedPreferences?.getString(KEY_LINKS, "")
                                    ?.let { text -> call.respondText(text) }
                            }

                            post("/updateUsage") {
                                try {
                                    val requestBody = call.receiveText()
                                    val jsonObject = JSONObject(requestBody)

                                    // 获取已使用的链接参数
                                    val usedKeyParamStr = if (jsonObject.has("usedKeyParam")) {
                                        jsonObject.getString("usedKeyParam")
                                    } else {
                                        "[]"
                                    }
                                    val usedKeyParam = JSONArray(usedKeyParamStr)

                                    // 获取未使用的链接参数
                                    val unusedKeyParamStr = if (jsonObject.has("unusedKeyParam")) {
                                        jsonObject.getString("unusedKeyParam")
                                    } else {
                                        "[]"
                                    }
                                    val unusedKeyParam = JSONArray(unusedKeyParamStr)

                                    val linksJson = sharedPreferences?.getString(KEY_LINKS, "")
                                    if (!linksJson.isNullOrEmpty()) {
                                        val type =
                                            object : TypeToken<MutableList<LinkData>>() {}.type
                                        val linksList =
                                            gson.fromJson<MutableList<LinkData>>(linksJson, type)

                                        // 标记已使用的链接
                                        for (i in 0 until usedKeyParam.length()) {
                                            val keyParam = usedKeyParam.getString(i)
                                            linksList.find { it.keyParam == keyParam }?.isUsed =
                                                true
                                        }

                                        // 标记未使用的链接
                                        for (i in 0 until unusedKeyParam.length()) {
                                            val keyParam = unusedKeyParam.getString(i)
                                            linksList.find { it.keyParam == keyParam }?.isUsed =
                                                false
                                        }

                                        // 保存更新后的数据
                                        sharedPreferences?.edit {
                                            putString(KEY_LINKS, gson.toJson(linksList))
                                        }

                                        call.respondText("Update successful")
                                        XposedBridge.log("yhhelper2025: Successfully updated link usage status")
                                    } else {
                                        call.respondText("No link data found")
                                        XposedBridge.log("yhhelper2025: No link data to update")
                                    }
                                } catch (e: Exception) {
                                    call.respondText("Update failed: ${e.message}")
                                    XposedBridge.log("yhhelper2025: Failed to update link usage status: ${e.message}")
                                }
                            }
                        }
                    }.start(wait = false)

                    XposedBridge.log("yhhelper2025: HTTP is running, port: 8892")
                } else {
                    XposedBridge.log("yhhelper2025: HTTP is already running")
                }
            } catch (e: Exception) {
                XposedBridge.log("yhhelper2025: HTTP is already running")
            }
        }
    }

    private fun extractPagePathFromXml(xml: String): String? {
        return try {
            val factory = XmlPullParserFactory.newInstance()
            factory.isNamespaceAware = true
            val parser = factory.newPullParser()

            parser.setInput(StringReader(xml))

            var eventType = parser.eventType
            while (eventType != XmlPullParser.END_DOCUMENT) {
                if (eventType == XmlPullParser.START_TAG && parser.name == "pagepath") {
                    return parser.nextText()
                }
                eventType = parser.next()
            }

            null
        } catch (e: Exception) {
            XposedBridge.log("yhhelper2025: error extracting pagepath: ${e.message}")
            null
        }
    }

    private fun extractAppIdFromXmlAppmsgAppid(xml: String): String? {
        val factory = XmlPullParserFactory.newInstance()
        factory.isNamespaceAware = true
        val parser = factory.newPullParser()

        parser.setInput(StringReader(xml))

        var eventType = parser.eventType
        while (eventType != XmlPullParser.END_DOCUMENT) {
            if (eventType == XmlPullParser.START_TAG && parser.name == "appmsg") {
                return parser.getAttributeValue(null, "appid")
            }
            eventType = parser.next()
        }

        return null
    }

    private fun extractAppIdFromXmlAppid(xml: String): String? {
        val factory = XmlPullParserFactory.newInstance()
        factory.isNamespaceAware = true
        val parser = factory.newPullParser()

        parser.setInput(StringReader(xml))

        var eventType = parser.eventType
        while (eventType != XmlPullParser.END_DOCUMENT) {
            if (eventType == XmlPullParser.START_TAG && parser.name == "appid") {
                return parser.nextText()
            }
            eventType = parser.next()
        }

        return null
    }

    /**
     * 从 weappinfo 标签中提取 appId
     * @param xml XML 内容
     * @return appId 或 null
     */
    private fun extractAppIdFromWeappinfo(xml: String): String? {
        return try {
            val factory = XmlPullParserFactory.newInstance()
            factory.isNamespaceAware = true
            val parser = factory.newPullParser()

            parser.setInput(StringReader(xml))

            var eventType = parser.eventType
            while (eventType != XmlPullParser.END_DOCUMENT) {
                if (eventType == XmlPullParser.START_TAG && parser.name == "appid" && parser.depth > 2) {
                    return parser.nextText()
                }
                eventType = parser.next()
            }

            null
        } catch (e: Exception) {
            XposedBridge.log("yhhelper2025: error extracting appid from weappinfo: ${e.message}")
            null
        }
    }

    /**
     * 从 weappinfo 标签中提取 pagePath
     * @param xml XML 内容
     * @return pagePath 或 null
     */
    private fun extractPagePathFromWeappinfo(xml: String): String? {
        return try {
            val factory = XmlPullParserFactory.newInstance()
            factory.isNamespaceAware = true
            val parser = factory.newPullParser()

            parser.setInput(StringReader(xml))

            var eventType = parser.eventType
            var inWeappinfo = false

            while (eventType != XmlPullParser.END_DOCUMENT) {
                if (eventType == XmlPullParser.START_TAG) {
                    if (parser.name == "weappinfo") {
                        inWeappinfo = true
                    } else if (inWeappinfo && parser.name == "pagepath") {
                        return parser.nextText()
                    }
                } else if (eventType == XmlPullParser.END_TAG && parser.name == "weappinfo") {
                    inWeappinfo = false
                }
                eventType = parser.next()
            }

            null
        } catch (e: Exception) {
            XposedBridge.log("yhhelper2025: error extracting pagepath from weappinfo: ${e.message}")
            null
        }
    }
}