package dev.pigmomo.yhhelper2025.utils

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.drawable.Icon
import android.os.Build
import android.service.quicksettings.Tile
import android.service.quicksettings.TileService
import androidx.annotation.RequiresApi
import dev.pigmomo.yhhelper2025.MainActivity
import dev.pigmomo.yhhelper2025.R

/**
 * 应用快速启动贴片服务
 * 用于在状态栏添加快速启动应用的贴片
 */
class AppTileService : TileService() {

    override fun onStartListening() {
        super.onStartListening()
        // 更新贴片状态
        val tile = qsTile
        tile?.let {
            it.label = "永助"
            it.icon = getIconDrawable()
            it.state = Tile.STATE_INACTIVE
            it.updateTile()
        }
    }

    @SuppressLint("StartActivityAndCollapseDeprecated")
    override fun onClick() {
        super.onClick()
        
        // 点击贴片时启动主应用
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        startActivityAndCollapse(intent)
    }

    private fun getIconDrawable() = Icon.createWithResource(this, R.drawable.ic_notification)
} 