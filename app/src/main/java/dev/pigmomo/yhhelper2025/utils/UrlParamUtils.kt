package dev.pigmomo.yhhelper2025.utils

/**
 * URL参数解析工具类
 * 用于解析不同活动中的URL参数
 */
object UrlParamUtils {

    /**
     * 从参数中提取TeamCode (16-19位数字)
     * 
     * @param param 可能包含TeamCode的参数
     * @return 提取的TeamCode，如果没有找到则返回null
     */
    fun extractTeamCode(param: String?): String? {
        if (param.isNullOrEmpty()) return null
        
        // 如果参数本身就是16-19位数字，直接返回
        if (param.length in 16..19 && param.all { it.isDigit() }) {
            return param
        }
        
        // 尝试从参数中提取TeamCode
        val teamCodePattern = Regex("teamCode=(\\d{16,19})")
        val teamCodeResult = teamCodePattern.find(param)
        return teamCodeResult?.groupValues?.get(1)
    }
    
    /**
     * 从参数中提取EventId (19位数字)
     * 
     * @param param 可能包含EventId的参数
     * @return 提取的EventId，如果没有找到则返回null
     */
    fun extractEventId(param: String?): String? {
        if (param.isNullOrEmpty()) return null
        
        // 如果参数本身就是19位数字，直接返回
        if (param.length == 19 && param.all { it.isDigit() }) {
            return param
        }
        
        // 尝试从参数中提取EventId
        val pattern = Regex("eventId=(\\d{19})")
        val matchResult = pattern.find(param)
        return matchResult?.groupValues?.get(1)
    }
    
    /**
     * 从参数中提取GameCode (19位数字)
     * 
     * @param param 可能包含GameCode的参数
     * @return 提取的GameCode，如果没有找到则返回null
     */
    fun extractGameCode(param: String?): String? {
        if (param.isNullOrEmpty()) return null
        
        // 如果参数本身就是19位数字，直接返回
        if (param.length == 19 && param.all { it.isDigit() }) {
            return param
        }
        
        // 尝试从参数中提取GameCode
        val gameCodePattern = Regex("gameCode=(\\d{19})")
        val gameCodeResult = gameCodePattern.find(param)
        return gameCodeResult?.groupValues?.get(1)
    }
    
    /**
     * 从参数中提取PrizeId (4位数字)
     * 
     * @param param 可能包含PrizeId的参数
     * @return 提取的PrizeId，如果没有找到则返回null
     */
    fun extractPrizeId(param: String?): String? {
        if (param.isNullOrEmpty()) return null
        
        // 尝试从参数中提取PrizeId
        val prizeIdPattern = Regex("prizeId=(\\d{4})")
        val prizeIdResult = prizeIdPattern.find(param)
        return prizeIdResult?.groupValues?.get(1)
    }
} 