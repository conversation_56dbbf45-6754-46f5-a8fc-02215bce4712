package dev.pigmomo.yhhelper2025.data.model

import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 链接数据类 - 存储微信消息中的链接相关信息
 * @param createTime 消息创建时间
 * @param keyParam 解析后的URL
 */
data class LinkData(
    val createTime: Long,
    val keyParam: String,
    var isUsed: Boolean = false
) {
    /**
     * 获取格式化的时间字符串
     * @return 格式化的时间字符串
     */
    fun getFormattedTime(): String {
        val date = Date(createTime)
        val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return formatter.format(date)
    }

    /**
     * 获取格式化的日期字符串（只包含日期部分）
     * @return 格式化的日期字符串
     */
    fun getFormattedDate(): String {
        val date = Date(createTime)
        val formatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        return formatter.format(date)
    }

    /**
     * 获取链接类型
     * @return 链接类型描述
     */
    fun getLinkType(): String {
        return when {
            keyParam.contains("prizeId") && keyParam.contains("gameCode") -> "助力券"
            keyParam.contains("eventId") -> "拼手气"
            keyParam.contains("teamCode") -> "组队瓜分"
            else -> ""
        }
    }

    companion object {
        /**
         * 从URL提取关键参数
         * @return 关键参数
         */
        fun getKeyParam(url: String): String {
            return when {
                url.contains("prizeId") && url.contains("gameCode") -> {
                    val prizeIdMatch = Regex("prizeId=([^&]+)").find(url)
                    val gameCodeMatch = Regex("gameCode=([^&]+)").find(url)
                    val prizeId = prizeIdMatch?.groupValues?.get(1) ?: ""
                    val gameCode = gameCodeMatch?.groupValues?.get(1) ?: ""
                    "prizeId=$prizeId&gameCode=$gameCode"
                }

                url.contains("eventId") -> {
                    val eventIdMatch = Regex("eventId=([^&]+)").find(url)
                    val eventId = eventIdMatch?.groupValues?.get(1) ?: ""
                    "eventId=$eventId"
                }

                url.contains("teamCode") -> {
                    val teamCodeMatch = Regex("teamCode=([^&]+)").find(url)
                    val teamCode = teamCodeMatch?.groupValues?.get(1) ?: ""
                    "teamCode=$teamCode"
                }

                else -> ""
            }
        }
    }
}