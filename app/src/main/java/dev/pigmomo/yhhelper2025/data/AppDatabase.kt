package dev.pigmomo.yhhelper2025.data

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import dev.pigmomo.yhhelper2025.data.local.dao.TokenDao
import dev.pigmomo.yhhelper2025.data.model.TokenEntity

@Database(entities = [TokenEntity::class], version = 1)
abstract class AppDatabase : RoomDatabase() {
    abstract fun tokenDao(): TokenDao

    companion object {
        private var instance: AppDatabase? = null
        
        @Synchronized
        fun getInstance(context: Context): AppDatabase {
            if (instance == null || instance?.isOpen == false) {
                synchronized(AppDatabase::class) {
                    if (instance?.isOpen == false) {
                        instance = null
                    }
                    
                    if (instance == null) {
                        instance = Room.databaseBuilder(
                            context.applicationContext,
                            AppDatabase::class.java,
                            "app_database"
                        ).build()
                    }
                }
            }
            return instance!!
        }
        
        // 添加强制重置方法
        @Synchronized
        fun resetInstance() {
            instance?.close()
            instance = null
        }
    }
}
