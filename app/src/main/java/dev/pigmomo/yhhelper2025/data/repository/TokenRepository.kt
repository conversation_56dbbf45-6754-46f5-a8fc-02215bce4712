package dev.pigmomo.yhhelper2025.data.repository

import android.util.Log
import dev.pigmomo.yhhelper2025.data.local.dao.TokenDao
import dev.pigmomo.yhhelper2025.data.model.TokenEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.regex.Pattern

/**
 * TokenRepository 类
 * 负责封装所有与Token相关的数据操作，作为数据源和ViewModel之间的中间层
 */
class TokenRepository(private val tokenDao: TokenDao) {

    // 用于存储数据库重连回调
    private var databaseReopenCallback: (() -> TokenDao)? = null

    /**
     * 设置数据库重连回调
     * @param callback 回调函数，返回新的TokenDao实例
     */
    fun setDatabaseReopenCallback(callback: () -> TokenDao) {
        databaseReopenCallback = callback
    }

    /**
     * 重新打开数据库连接
     * 当数据库连接失效时调用此方法
     * @return 是否成功重新打开连接
     */
    fun reopenDatabase(): Boolean {
        return try {
            Log.d("TokenRepository", "Attempting to reopen database connection")
            databaseReopenCallback?.let {
                // 使用回调获取新的TokenDao实例
                val newDao = it.invoke()
                // 使用反射设置新的TokenDao实例
                val field = this::class.java.getDeclaredField("tokenDao")
                field.isAccessible = true
                field.set(this, newDao)
                Log.d("TokenRepository", "Database connection reopened successfully")
                true
            } ?: run {
                Log.e("TokenRepository", "Cannot reopen database: callback not set")
                false
            }
        } catch (e: Exception) {
            Log.e("TokenRepository", "Failed to reopen database: ${e.message}")
            false
        }
    }

    /**
     * 获取所有Token
     * @return Flow<List<TokenEntity>> Token列表的数据流
     */
    fun getAllTokens(): Flow<List<TokenEntity>> = flow {
        try {
            emit(tokenDao.getAllTokens())
        } catch (e: Exception) {
            // 数据库可能关闭，返回空列表
            Log.e("TokenRepository", "Failed to get all tokens: ${e.message}")
            emit(emptyList())
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 根据uid获取Token
     * @param uid uid
     * @return Token实体或null
     */
    fun getTokenByUid(uid: String): Flow<TokenEntity?> = flow {
        try {
            emit(tokenDao.getTokenByUid(uid))
        } catch (e: Exception) {
            // 数据库可能关闭，返回null
            emit(null)
        }
    }.flowOn(Dispatchers.IO)


    suspend fun getTokensToHelpCoupon(prizeId: String, isNew: Boolean): List<TokenEntity> {
        return withContext(Dispatchers.IO) {
            try {
                //当前日期格式化为1970-01-01
                val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                val today = sdf.format(System.currentTimeMillis())

                val tokens = if (isNew) {
                    tokenDao.getAllNewTokens().reversed()
                } else {
                    tokenDao.getAllTokens()
                }
                tokens.filter { token ->
                    // 排除已标记为活动限制的Token
                    !token.activityLimited && 
                    run {
                        //{"1234":["1970-01-01",0,["0000"]]}
                        val helpCouponCount = JSONObject(token.helpCouponCount)
                        if (helpCouponCount.has(prizeId)) {
                            //排除今日已助力Token
                            val lastHelpTimeStr = helpCouponCount.getJSONArray(prizeId).getString(0)
                            lastHelpTimeStr != today
                        } else {
                            true
                        }
                    }
                }
            } catch (e: Exception) {
                // 数据库可能关闭，返回空列表
                emptyList()
            }
        }
    }

    /**
     * 获取可用于积分组队的Tokens
     * @param teamCode teamCode
     * @param isNew 是否使用新Token
     * @return Token列表
     */
    suspend fun getTokensToPointExchange(teamCode: String, isNew: Boolean): List<TokenEntity> {
        return withContext(Dispatchers.IO) {
            try {
                val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                val today = sdf.format(System.currentTimeMillis())

                val tokens = if (isNew) {
                    tokenDao.getAllNewTokens().reversed()
                } else {
                    tokenDao.getAllTokens().filter { it.extraNote.isNotEmpty() }
                }

                tokens.filter { token ->
                    // 排除已标记为活动限制的Token
                    !token.activityLimited && 
                    run {
                        // pointExchangeCount字段中的数据格式: ["1970-01-01",0,["0000"]]
                        val pointExchangeArr = JSONArray(token.pointExchangeCount)
                        val pointExchangeCount = pointExchangeArr.getJSONArray(2)
                        if (pointExchangeCount.toString().contains(teamCode)) {
                            //排除今日已助力Token
                            val lastHelpTimeStr = pointExchangeArr.getString(0)
                            lastHelpTimeStr != today
                        } else {
                            true
                        }
                    }
                }.shuffled() // 随机排序Token列表
            } catch (e: Exception) {
                // 数据库可能关闭，返回空列表
                emptyList()
            }
        }
    }

    /**
     * 获取可用于拼手气的Tokens
     * @param eventId 事件ID
     * @return Token列表
     */
    suspend fun getTokensToSpellLuck(eventId: String): List<TokenEntity> {
        return withContext(Dispatchers.IO) {
            try {
                // 当前日期格式化为1970-01-01
                val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                val today = sdf.format(System.currentTimeMillis())

                val tokens = tokenDao.getAllTokens()

                tokens.filter { token ->
                    // 排除已标记为活动限制的Token
                    !token.activityLimited && 
                    run {
                        // spellLuckCount字段中的数据格式: ["1970-01-01",0,["0000"]]
                        val spellLuckCountArr = JSONArray(token.spellLuckCount)
                        val lastHelpTimeStr = spellLuckCountArr.getString(0)

                        lastHelpTimeStr != today
                    }
                }
            } catch (e: Exception) {
                // 数据库可能关闭，返回空列表
                emptyList()
            }
        }
    }

    /**
     * 插入Token
     * @param token Token实体
     * @return Pair<Boolean, Boolean> 是否已存在，是否插入成功
     */
    suspend fun insertToken(token: TokenEntity): Pair<Boolean, Boolean> {
        return withContext(Dispatchers.IO) {
            try {
                // 检查是否存在相同的token
                val tokenFind = tokenDao.getTokenByUid(token.uid)

                if (tokenFind != null) {
                    //更新phoneNumber/userKey/accessToken/refreshToken/expiresIn/deviceId/signInParams
                    val updateToken = tokenFind.copy(
                        phoneNumber = token.phoneNumber,
                        userKey = token.userKey,
                        accessToken = token.accessToken,
                        refreshToken = token.refreshToken,
                        expiresIn = token.expiresIn,
                        appParam = token.appParam
                    )

                    val updateResult = tokenDao.updateToken(updateToken)
                    Pair(true, updateResult != -1)
                } else {
                    tokenDao.insertToken(token)
                    Pair(false, true)
                }
            } catch (e: Exception) {
                // 数据库可能关闭或操作失败
                Pair(false, false)
            }
        }
    }

    /**
     * 更新Token
     * @param token Token实体
     * @return Int 更新的行数
     */
    suspend fun updateToken(token: TokenEntity): Int {
        return withContext(Dispatchers.IO) {
            try {
                tokenDao.updateToken(token)
            } catch (e: Exception) {
                // 数据库可能关闭或操作失败
                -1
            }
        }
    }

    /**
     * 删除Token
     * @param uid uid
     */
    suspend fun deleteTokenByUid(uid: String) {
        withContext(Dispatchers.IO) {
            try {
                tokenDao.deleteTokenByUid(uid)
            } catch (e: Exception) {
                // 数据库可能关闭或操作失败，忽略异常
            }
        }
    }

    /**
     * 整理Token数据库
     * 排序
     */
    suspend fun sortTokens() {
        return withContext(Dispatchers.IO) {
            try {
                val tokens = tokenDao.getAllTokens()

                val dateFormat = SimpleDateFormat("MM.dd.yy", Locale.getDefault())

                val sortedTokens = tokens.sortedWith(
                    compareBy<TokenEntity>(
                        { it.isNew }, // isNew=false排在前面
                        { it.activityLimited }, // activityLimited=true排在后面
                        {
                            try {
                                dateFormat.parse(it.updateDate) // 按时间排序
                            } catch (e: Exception) {
                                null
                            }
                        },
                        { it.extraNote } // 相同extraNote排在一起
                    ))

                tokenDao.deleteAllTokens()
                sortedTokens.forEach { token ->
                    tokenDao.insertToken(token)
                }
            } catch (e: Exception) {
                // 数据库可能关闭或操作失败，忽略异常
            }
        }
    }
}