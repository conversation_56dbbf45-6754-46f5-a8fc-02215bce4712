package dev.pigmomo.yhhelper2025.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import dev.pigmomo.yhhelper2025.MainActivity

/**
 * Token数据库实体/Token实体
 */
@Entity(tableName = "Tokens")
data class TokenEntity(
    @PrimaryKey
    //memberId/uid/userId
    val uid: String,
    val phoneNumber: String,
    val userKey: String,
    val accessToken: String,
    val refreshToken: String,
    val expiresIn: Long,
    //05.08.25
    val updateDate: String,
    //是否有新人特权（新人价、新人券）
    var isNew: Boolean,
    //是否首次砍价
    var bargainFirst: Boolean,
    //是否参加活动火爆
    var activityLimited: Boolean,
    //是否使用/购买永辉卡风险
    var yhCardLimited: Boolean,
    //channel,screen,deviceid,distinctId,osVersion,model,networkType,brand,version
    //vivo,1080x2400,b1aa9816-bdca-4abc-b342-0b613aad431f,38219ba1-7015-4e2f-925b-0343f516b380,android33,vivo,WIFI,V2343A,appVersion
    val appParam: String,
    var extraNote: String,

    //helpCouponCount = "{\"1234\":[\"1970-01-01\",0,[\"0000\"]]}", //prizeID:[lastHelpTimeStr,totalHelpCount,totalHelpedGameCode]
    var helpCouponCount: String,
    //pointExchangeCount = "[\"1970-01-01\",0,[\"0000\"]]", //[lastHelpTimeStr,totalHelpCount,totalHelpedTeamCode]
    var pointExchangeCount: String,
    //spellLuckCount = "[\"1970-01-01\",0,[\"0000\"]]", //[lastHelpTimeStr,totalHelpCount,totalEventId]
    var spellLuckCount: String,
) {
    companion object {
        /**
         * 从字符串解析为TokenEntity对象
         * 
         * @param tokenString TokenEntity的toString()输出格式字符串
         * @return 解析后的TokenEntity对象，如果解析失败则返回null
         */
        fun fromString(tokenString: String): TokenEntity? {
            try {
                // 确保字符串是TokenEntity格式
                if (!tokenString.startsWith("TokenEntity(") || !tokenString.endsWith(")")) {
                    return null
                }
                
                // 提取括号内的内容
                val content = tokenString.substring("TokenEntity(".length, tokenString.length - 1)
                
                //uid=720720324235167663, phoneNumber=16621642869, userKey=3a0d7c3f15e6d03af3ea3326692312b5ea7d059848bcf944c073737316ecf53ee056aecbd2e900ed1afc3e064d3ba346f912d80a232f78e49407ec0f53b0e81a2214ec8956101d1c311892e6aabdb12e, accessToken=601933_C984B6C0DBA80AA519F73BF1DE6C8B6B980EA2651557C3C33B7D9DCF911CA7C95A4C47A6669B03738D46490A5DA18DA1, refreshToken=9fee8c21-c26c-43c5-bdb7-af0b75185348, expiresIn=0, updateDate=04.14.25, isNew=true, bargainFirst=true, activityLimited=false, yhCardLimited=false, appParam=xiaomi,1284x2778,f72246e1-8c08-4f82-80b8-c089170e1ed4,5815d8e0-26aa-4a6b-ac37-4fc16519a6c6,android33,Xiaomi,WIFI,2410DPN6CC,11.2.6.0, extraNote=新人特权/积分0, helpCouponCount={"0000":["1970-01-01",0,["0000"]]}, pointExchangeCount=["1970-01-01",0,["0000"]], spellLuckCount=["1970-01-01",0,["0000"]]
                val tokenArray = content.split(", ")

                val fieldMap = tokenArray.map { it.split("=") }.associate { it[0] to it[1] }
                
                // 创建TokenEntity对象
                return TokenEntity(
                    uid = fieldMap["uid"] ?: "",
                    phoneNumber = fieldMap["phoneNumber"] ?: "",
                    userKey = fieldMap["userKey"] ?: "",
                    accessToken = fieldMap["accessToken"] ?: "",
                    refreshToken = fieldMap["refreshToken"] ?: "",
                    expiresIn = fieldMap["expiresIn"]?.toLongOrNull() ?: 0,
                    updateDate = fieldMap["updateDate"] ?: "",
                    isNew = fieldMap["isNew"]?.toBoolean() ?: false,
                    bargainFirst = fieldMap["bargainFirst"]?.toBoolean() ?: false,
                    activityLimited = fieldMap["activityLimited"]?.toBoolean() ?: false,
                    yhCardLimited = fieldMap["yhCardLimited"]?.toBoolean() ?: false,
                    appParam = fieldMap["appParam"] ?: "",
                    extraNote = fieldMap["extraNote"] ?: "",
                    helpCouponCount = fieldMap["helpCouponCount"] ?: "",
                    pointExchangeCount = fieldMap["pointExchangeCount"] ?: "",
                    spellLuckCount = fieldMap["spellLuckCount"] ?: ""
                )
            } catch (e: Exception) {
                e.printStackTrace()
                return null
            }
        }
    }
}