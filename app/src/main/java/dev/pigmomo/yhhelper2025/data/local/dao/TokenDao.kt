package dev.pigmomo.yhhelper2025.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Update
import dev.pigmomo.yhhelper2025.data.model.TokenEntity

@Dao
interface TokenDao {
    /**
     * 获取所有Token
     * @return Token列表
     */
    @Query("SELECT * FROM Tokens")
    suspend fun getAllTokens(): List<TokenEntity>

    /**
     * 获取所有isNew=true的Token
     * @return Token列表
     */
    @Query("SELECT * FROM Tokens WHERE isNew = 1")
    suspend fun getAllNewTokens(): List<TokenEntity>

    /**
     * 删除所有Token
     */
    @Query("DELETE FROM Tokens")
    suspend fun deleteAllTokens()

    /**
     * 插入Token
     * @param token Token实体
     */
    @Insert
    suspend fun insertToken(token: TokenEntity)

    /**
     * 更新Token
     * @param token Token实体
     * @return Int 更新的行数
     */
    @Update
    suspend fun updateToken(token: TokenEntity): Int

    /**
     * 根据uid获取Token
     * @param uid uid
     * @return Token实体或null
     */
    @Query("SELECT * FROM Tokens WHERE uid = :uid")
    suspend fun getTokenByUid(uid: String): TokenEntity?

    /**
     * 根据uid删除Token
     * @param uid uid
     */
    @Query("DELETE FROM Tokens WHERE uid = :uid")
    suspend fun deleteTokenByUid(uid: String)

    /**
     * 根据uid更新HelpCouponCount
     * @param uid uid
     * @param helpCouponCount HelpCouponCount
     * @return 更新的行数
     */
    @Query("UPDATE Tokens SET helpCouponCount = :helpCouponCount WHERE uid = :uid")
    suspend fun updateHelpCouponCountByUid(uid: String, helpCouponCount: String): Int

    /**
     * 根据uid更新PointExchangeCount
     * @param uid uid
     * @param pointExchangeCount PointExchangeCount
     * @return 更新的行数
     */
    @Query("UPDATE Tokens SET pointExchangeCount = :pointExchangeCount WHERE uid = :uid")
    suspend fun updatePointExchangeCountByUid(uid: String, pointExchangeCount: String): Int

    /**
     * 根据uid更新SpellLuckCount
     * @param uid uid
     * @param spellLuckCount SpellLuckCount
     * @return 更新的行数
     */
    @Query("UPDATE Tokens SET spellLuckCount = :spellLuckCount WHERE uid = :uid")
    suspend fun updateSpellLuckCountByUid(uid: String, spellLuckCount: String): Int

    /**
     * 根据备注搜索Token（模糊匹配）
     * @param keyword 搜索关键词
     * @return Token列表
     */
    @Query("SELECT * FROM Tokens WHERE extraNote LIKE '%' || :keyword || '%'")
    suspend fun getTokensByNoteContains(keyword: String): List<TokenEntity>

    /**
     * 根据备注搜索Token（精确匹配）
     * @param keyword 搜索关键词
     * @return Token列表
     */
    @Query("SELECT * FROM Tokens WHERE extraNote = :keyword")
    suspend fun getTokensByNoteEquals(keyword: String): List<TokenEntity>
}