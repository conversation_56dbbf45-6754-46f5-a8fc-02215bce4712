package dev.pigmomo.yhhelper2025.ui.viewmodel

import android.content.Context
import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.pigmomo.yhhelper2025.data.AppDatabase
import dev.pigmomo.yhhelper2025.data.model.TokenEntity
import dev.pigmomo.yhhelper2025.data.repository.TokenRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class SettingViewModel(applicationContext: Context) : ViewModel() {
    // 使用applicationContext防止内存泄漏
    private val appContext = applicationContext.applicationContext

    // 状态流
    private val _recovering = MutableStateFlow(false)
    val recovering: StateFlow<Boolean> = _recovering.asStateFlow()

    private val _backup = MutableStateFlow(false)
    val backup: StateFlow<Boolean> = _backup.asStateFlow()

    private val _sorted = MutableStateFlow(false)
    val sorted: StateFlow<Boolean> = _sorted.asStateFlow()

    private val _inputting = MutableStateFlow(false)
    val inputting: StateFlow<Boolean> = _inputting.asStateFlow()

    private val _accountExporting = MutableStateFlow(false)
    val accountExporting: StateFlow<Boolean> = _accountExporting.asStateFlow()

    private val _backupExporting = MutableStateFlow(false)
    val backupExporting: StateFlow<Boolean> = _backupExporting.asStateFlow()

    // 操作结果回调
    private val _operationResult = MutableStateFlow<OperationResult?>(null)
    val operationResult: StateFlow<OperationResult?> = _operationResult.asStateFlow()

    // 数据库路径
    private val databasePath: String by lazy {
        appContext.getDatabasePath("app_database").absolutePath
    }

    // 备份数据库到指定URI
    fun backupDatabase(uri: Uri) {
        _backup.value = true
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    // 不关闭数据库连接，直接复制文件
                    appContext.contentResolver.openOutputStream(uri)?.use { outputStream ->
                        FileInputStream(File(databasePath)).use { inputStream ->
                            inputStream.copyTo(outputStream)
                        }
                    }
                }
                _operationResult.value = OperationResult.Success("数据库备份成功")
            } catch (e: Exception) {
                e.printStackTrace()
                _operationResult.value = OperationResult.Error("备份失败: ${e.message}")
            } finally {
                _backup.value = false
            }
        }
    }

    // 从指定URI恢复数据库
    fun restoreDatabase(uri: Uri) {
        _recovering.value = true
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    // 关闭并重置数据库实例
                    AppDatabase.resetInstance()

                    // 复制选择的文件到数据库路径
                    appContext.contentResolver.openInputStream(uri)?.use { inputStream ->
                        FileOutputStream(File(databasePath)).use { outputStream ->
                            inputStream.copyTo(outputStream)
                        }
                    }
                }
                _operationResult.value = OperationResult.Success("数据库恢复成功")
            } catch (e: Exception) {
                e.printStackTrace()
                _operationResult.value = OperationResult.Error("恢复失败: ${e.message}")
            } finally {
                _recovering.value = false
            }
        }
    }

    // 执行数据库整理
    fun sortDatabase() {
        _sorted.value = true
        viewModelScope.launch {
            try {
                val tokenDao = AppDatabase.getInstance(appContext).tokenDao()
                val tokenRepository = TokenRepository(tokenDao)

                // 调用TokenRepository的sortTokens方法
                tokenRepository.sortTokens()

                // 重置ViewModelFactory实例
                // ViewModelFactory.resetInstance()

                _operationResult.value = OperationResult.Success("数据库整理成功")
            } catch (e: Exception) {
                e.printStackTrace()
                _operationResult.value = OperationResult.Error("整理失败: ${e.message}")
            } finally {
                _sorted.value = false
            }
        }
    }

    // 从指定URI导入账号
    fun importAccounts(uri: Uri) {
        _inputting.value = true
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    val tokenDao = AppDatabase.getInstance(appContext).tokenDao()
                    val tokenRepository = TokenRepository(tokenDao)
                    val content = appContext.contentResolver.openInputStream(uri)?.use { inputStream ->
                        inputStream.bufferedReader().use { it.readText() }
                    } ?: ""

                    if (content.isEmpty()) {
                        _operationResult.value = OperationResult.Error("导入文件为空")
                        return@withContext
                    }

                    val lines = content.split("\n")
                    val result = processAccountLines(lines, tokenRepository)

                    _operationResult.value = OperationResult.Success("导入${result.importedCount}个账号，更新${result.updatedCount}个账号")
                }
            } catch (e: Exception) {
                e.printStackTrace()
                _operationResult.value = OperationResult.Error("导入失败: ${e.message}")
            } finally {
                _inputting.value = false
            }
        }
    }

    // 导出账号到指定URI
    fun exportAccountsToUri(uri: Uri, startIndex: Int, endIndex: Int) {
        _accountExporting.value = true
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    val tokenDao = AppDatabase.getInstance(appContext).tokenDao()
                    val allTokens = tokenDao.getAllTokens()

                    if (allTokens.isEmpty()) {
                        _operationResult.value = OperationResult.Error("数据库中没有数据")
                        return@withContext
                    }

                    if (startIndex <= 0 || startIndex > allTokens.size || endIndex <= 0 || endIndex > allTokens.size) {
                        _operationResult.value = OperationResult.Error("超出数据库范围，当前共有${allTokens.size}条数据")
                        return@withContext
                    }

                    val tokensToExport = allTokens.subList(startIndex - 1, endIndex)
                    val exportContent = tokensToExport.joinToString("\n") { token ->
                        "${token.phoneNumber}----${token.uid}----${token.userKey}-601933-${token.accessToken}----${token.refreshToken}----${token.appParam},${token.expiresIn}----${token.updateDate} ${token.extraNote}"
                    }

                    // 写入到用户选择的URI
                    appContext.contentResolver.openOutputStream(uri)?.use { outputStream ->
                        outputStream.write(exportContent.toByteArray())
                    }

                    // 删除已导出的账号
                    tokensToExport.forEach { token ->
                        tokenDao.deleteTokenByUid(token.uid)
                    }

                    _operationResult.value = OperationResult.Success("成功导出${tokensToExport.size}个账号")
                }
            } catch (e: Exception) {
                e.printStackTrace()
                _operationResult.value = OperationResult.Error("导出失败: ${e.message}")
            } finally {
                _accountExporting.value = false
            }
        }
    }

    // 备份导出到指定URI
    fun exportBackupToUri(uri: Uri, startIndex: Int, endIndex: Int) {
        _backupExporting.value = true
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    val tokenDao = AppDatabase.getInstance(appContext).tokenDao()
                    val allTokens = tokenDao.getAllTokens()

                    if (allTokens.isEmpty()) {
                        _operationResult.value = OperationResult.Error("数据库中没有数据")
                        return@withContext
                    }

                    if (startIndex <= 0 || startIndex > allTokens.size || endIndex <= 0 || endIndex > allTokens.size) {
                        _operationResult.value = OperationResult.Error("超出数据库范围，当前共有${allTokens.size}条数据")
                        return@withContext
                    }

                    val tokensToExport = allTokens.subList(startIndex - 1, endIndex)
                    val exportContent = tokensToExport.joinToString("\n") { token ->
                        "${token.phoneNumber}----${token.uid}----${token.userKey}-601933-${token.accessToken}----${token.refreshToken}----${token.appParam},${token.expiresIn}----${token.updateDate} ${token.extraNote}"
                    }

                    // 写入到用户选择的URI
                    appContext.contentResolver.openOutputStream(uri)?.use { outputStream ->
                        outputStream.write(exportContent.toByteArray())
                    }

                    _operationResult.value = OperationResult.Success("成功备份${tokensToExport.size}个账号")
                }
            } catch (e: Exception) {
                e.printStackTrace()
                _operationResult.value = OperationResult.Error("备份导出失败: ${e.message}")
            } finally {
                _backupExporting.value = false
            }
        }
    }

    // 清除操作结果
    fun clearOperationResult() {
        _operationResult.value = null
    }

    /**
     * 处理导入的账号行
     * @param lines 账号行列表
     * @param tokenRepository Token仓库
     * @return 导入结果
     */
    private suspend fun processAccountLines(
        lines: List<String>,
        tokenRepository: TokenRepository
    ): ImportResult {
        var importedCount = 0
        var updatedCount = 0
        val currentTimeStamp = System.currentTimeMillis()
        val currentDate = SimpleDateFormat("MM.dd.yy", Locale.getDefault()).format(Date())

        for (line in lines) {
            if (line.isBlank()) continue

            val parts = line.split("----")
            val token = when (parts.size) {
                5 -> parseAccountWith5Parts(parts, currentTimeStamp, currentDate)
                6 -> parseAccountWith6Parts(parts, currentTimeStamp, currentDate)
                else -> null
            } ?: continue

            val insertResult = tokenRepository.insertToken(token)
            if (insertResult.first) {
                updatedCount++
            } else {
                importedCount++
            }
        }

        return ImportResult(importedCount, updatedCount)
    }

    /**
     * 解析5部分的账号行
     * @param parts 分割后的部分
     * @param currentTimeStamp 当前时间戳
     * @param currentDate 当前日期
     * @return Token实体或null
     */
    private fun parseAccountWith5Parts(
        parts: List<String>,
        currentTimeStamp: Long,
        currentDate: String
    ): TokenEntity? {
        val phoneNumber = parts[0]
        val uid = parts[1]
        val userKeyAndAccessToken = parts[2].split("-601933-")

        // 旧格式兼容，检查格式
        if (userKeyAndAccessToken.size != 2) {
            return null
        }

        val userKey = userKeyAndAccessToken[0]
        val accessToken = userKeyAndAccessToken[1]
        val refreshToken = parts[3]
        val appParamAndExpiresIn = parts[4].split(",")

        // 处理appParam和expiresIn
        val (appParam, expiresIn) = processAppParamAndExpiresIn(
            appParamAndExpiresIn,
            currentTimeStamp
        )

        // 设置默认的JSON字符串格式
        val defaultHelpCouponCount = "{\"1234\":[\"1970-01-01\",0,[\"0000\"]]}"
        val defaultPointExchangeCount = "[\"1970-01-01\",0,[\"0000\"]]"
        val defaultSpellLuckCount = "[\"1970-01-01\",0,[\"0000\"]]"

        return TokenEntity(
            uid = uid,
            phoneNumber = phoneNumber,
            userKey = userKey,
            accessToken = accessToken,
            refreshToken = refreshToken,
            expiresIn = expiresIn,
            updateDate = currentDate,
            isNew = true,
            bargainFirst = true,
            activityLimited = false,
            yhCardLimited = false,
            appParam = appParam,
            extraNote = "",
            helpCouponCount = defaultHelpCouponCount,
            pointExchangeCount = defaultPointExchangeCount,
            spellLuckCount = defaultSpellLuckCount
        )
    }

    /**
     * 解析6部分的账号行
     * @param parts 分割后的部分
     * @param currentTimeStamp 当前时间戳
     * @param currentDate 当前日期
     * @return Token实体或null
     */
    private fun parseAccountWith6Parts(
        parts: List<String>,
        currentTimeStamp: Long,
        currentDate: String
    ): TokenEntity? {
        val phoneNumber = parts[0]
        val uid = parts[1]
        val userKeyAndAccessToken = parts[2].split("-601933-")

        // 检查格式
        if (userKeyAndAccessToken.size != 2) {
            return null
        }

        val userKey = userKeyAndAccessToken[0]
        val accessToken = userKeyAndAccessToken[1]
        val refreshToken = parts[3]
        val appParamAndExpiresIn = parts[4].split(",")

        // 处理appParam和expiresIn
        val (appParam, expiresIn) = processAppParamAndExpiresIn(
            appParamAndExpiresIn,
            currentTimeStamp
        )

        // 处理updateDate和extraNote
        val (updateDate, extraNote) = processUpdateDateAndExtraNote(parts[5], currentDate)

        // 设置默认的JSON字符串格式
        val defaultHelpCouponCount = "{\"1234\":[\"1970-01-01\",0,[\"0000\"]]}"
        val defaultPointExchangeCount = "[\"1970-01-01\",0,[\"0000\"]]"
        val defaultSpellLuckCount = "[\"1970-01-01\",0,[\"0000\"]]"

        return TokenEntity(
            uid = uid,
            phoneNumber = phoneNumber,
            userKey = userKey,
            accessToken = accessToken,
            refreshToken = refreshToken,
            expiresIn = expiresIn,
            updateDate = updateDate,
            isNew = true,
            bargainFirst = true,
            activityLimited = false,
            yhCardLimited = false,
            appParam = appParam,
            extraNote = extraNote,
            helpCouponCount = defaultHelpCouponCount,
            pointExchangeCount = defaultPointExchangeCount,
            spellLuckCount = defaultSpellLuckCount
        )
    }

    /**
     * 处理appParam和expiresIn
     * @param appParamAndExpiresIn appParam和expiresIn的分割部分
     * @param currentTimeStamp 当前时间戳
     * @return Pair<String, Long> appParam和expiresIn
     */
    private fun processAppParamAndExpiresIn(
        appParamAndExpiresIn: List<String>,
        currentTimeStamp: Long
    ): Pair<String, Long> {
        return when (appParamAndExpiresIn.size) {
            8 -> {
                // 只有appParam，缺少version和expiresIn
                Pair(
                    appParamAndExpiresIn.joinToString(",") + ",11.2.6.0",
                    currentTimeStamp
                )
            }

            9 -> {
                // 有appParam和version，缺少expiresIn
                Pair(
                    appParamAndExpiresIn.joinToString(","),
                    currentTimeStamp
                )
            }

            10 -> {
                // 有完整参数
                val expIn = appParamAndExpiresIn[9].toLongOrNull()
                    ?: currentTimeStamp
                Pair(
                    // 删除第10个expIn参数
                    appParamAndExpiresIn.subList(0, 9).joinToString(","),
                    expIn
                )
            }

            else -> {
                Pair("", currentTimeStamp)
            }
        }
    }

    /**
     * 处理updateDate和extraNote
     * @param part 第6部分
     * @param currentDate 当前日期
     * @return Pair<String, String> updateDate和extraNote
     */
    private fun processUpdateDateAndExtraNote(
        part: String,
        currentDate: String
    ): Pair<String, String> {
        val dateRegex = Regex("\\d{2}\\.\\d{2}\\.\\d{2}")
        return if (dateRegex.containsMatchIn(part)) {
            // 包含日期格式，分割updateDate和extraNote
            val updateDateAndExtraNote = part.split(" ", limit = 2)
            Pair(
                updateDateAndExtraNote[0],
                if (updateDateAndExtraNote.size > 1) updateDateAndExtraNote[1] else ""
            )
        } else {
            // 不包含日期格式，使用当前日期
            Pair(currentDate, part)
        }
    }

    /**
     * 导入结果数据类
     */
    data class ImportResult(
        val importedCount: Int,
        val updatedCount: Int
    )

    /**
     * 操作结果密封类
     */
    sealed class OperationResult {
        data class Success(val message: String) : OperationResult()
        data class Error(val message: String) : OperationResult()
    }
} 
