package dev.pigmomo.yhhelper2025.ui.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

/**
 * 设置页面通用按钮组件
 *
 * @param text 按钮文本
 * @param loadingText 加载中文本
 * @param isLoading 是否处于加载状态
 * @param onClick 点击回调
 * @param modifier 修饰符
 * @param paddingValues 内边距值，默认为左右20dp
 */
@Composable
fun SettingButton(
    text: String,
    loadingText: String,
    isLoading: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    paddingValues: Modifier = Modifier.padding(start = 20.dp, end = 20.dp)
) {
    Button(
        modifier = modifier
            .then(paddingValues)
            .height(50.dp)
            .fillMaxWidth(),
        enabled = !isLoading,
        onClick = onClick,
        shape = RoundedCornerShape(8.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = if (!isLoading) MaterialTheme.colorScheme.primary else Color(
                0xFFFFCDD2
            ),
            disabledContainerColor = Color(
                0xFFFFCDD2
            )
        )
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Start
        ) {
            Text(if (!isLoading) text else loadingText)
        }
    }
} 