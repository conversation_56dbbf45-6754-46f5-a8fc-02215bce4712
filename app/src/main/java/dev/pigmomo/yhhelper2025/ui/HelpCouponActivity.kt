package dev.pigmomo.yhhelper2025.ui

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.os.Bundle
import android.view.ViewGroup
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.outlined.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.outlined.MoreVert
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.BottomAppBarDefaults
import androidx.compose.material3.Button
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.ViewModelProvider
import dev.pigmomo.yhhelper2025.R
import dev.pigmomo.yhhelper2025.data.model.ShopInfo
import dev.pigmomo.yhhelper2025.data.model.TokenEntity
import dev.pigmomo.yhhelper2025.ui.theme.Yhhelper2025Theme
import dev.pigmomo.yhhelper2025.ui.viewmodel.HelpCouponViewModel
import dev.pigmomo.yhhelper2025.ui.viewmodel.ViewModelFactory
import dev.pigmomo.yhhelper2025.utils.WebUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 助力券活动界面
 */
class HelpCouponActivity : ComponentActivity() {
    // ViewModel实例
    private lateinit var viewModel: HelpCouponViewModel
    // 从Intent传递的keyParam参数
    private var keyParam: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 获取Intent中的keyParam参数
        keyParam = intent.getStringExtra("keyParam")

        // 初始化ViewModel
        viewModel = ViewModelProvider(this, ViewModelFactory.getInstance(this))[HelpCouponViewModel::class.java]

        setContent {
            Yhhelper2025Theme(dynamicColor = false) {
                HelpCouponScreen(viewModel, this, keyParam)
            }
        }
    }
}

/**
 * 助力券界面组件
 *
 * @param viewModel 助力券ViewModel
 * @param activity 活动实例
 * @param keyParam 从Intent传递的keyParam参数
 */
@SuppressLint("SetJavaScriptEnabled")
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun HelpCouponScreen(viewModel: HelpCouponViewModel, activity: HelpCouponActivity, keyParam: String? = null) {
    val context = LocalContext.current
    val clipboardManager = LocalClipboardManager.current
    val focusManager = LocalFocusManager.current
    val coroutineScope = rememberCoroutineScope()

    // 从ViewModel获取状态
    val shopInfoList by viewModel.shopInfoList.collectAsState()
    val tokens by viewModel.tokens.collectAsState()

    // WebView实例
    var helpCouponWebView: WebView by remember { mutableStateOf(WebView(context)) }

    // 初始化选中的商店信息
    LaunchedEffect(shopInfoList) {
        if (viewModel.selectedShopInfo == null && shopInfoList.isNotEmpty()) {
            viewModel.selectedShopInfo = shopInfoList[0]
        }
    }

    // 检查keyParam或剪贴板中是否有PrizeID和GameCode
    LaunchedEffect(Unit) {
        // 如果有keyParam参数，则直接使用，不从剪贴板获取
        if (!keyParam.isNullOrEmpty()) {
            if (viewModel.processKeyParam(keyParam)) {
                return@LaunchedEffect
            }
        }
        
        // 只有当没有keyParam参数或解析失败时，才尝试从剪贴板获取
        viewModel.processClipboardContent(clipboardManager)
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Row {
                        Text("助力券", maxLines = 1)
                        Text(
                            viewModel.helpCouponTips,
                            fontSize = 10.sp,
                            maxLines = 1,
                            modifier = Modifier
                                .padding(start = 5.dp)
                                .offset(y= (5).dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    actionIconContentColor = Color.White
                ),
                navigationIcon = {
                    IconButton(onClick = { activity.finish() }) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Outlined.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                actions = {
                    IconButton(onClick = {
                        viewModel.settingExpanded = !viewModel.settingExpanded
                    }) {
                        Icon(
                            imageVector = Icons.Outlined.MoreVert,
                            contentDescription = "设置",
                            tint = Color.White
                        )

                        HelpCouponShopSelectionMenu(
                            expanded = viewModel.settingExpanded,
                            onDismissRequest = { viewModel.settingExpanded = false },
                            needNewToken = viewModel.needNewToken,
                            onNeedNewTokenChange = { viewModel.needNewToken = it },
                            shopInfoList = shopInfoList,
                            selectedShopInfo = viewModel.selectedShopInfo,
                            onShopSelected = { shopInfo ->
                                viewModel.selectedShopInfo = shopInfo
                                viewModel.settingExpanded = false
                                viewModel.loadInitialUrl(helpCouponWebView)
                            }
                        )
                    }
                }
            )
        },
        bottomBar = {
            BottomAppBar(
                actions = {
                    OutlinedTextField(
                        value = viewModel.inputPrizeId,
                        onValueChange = { newValue ->
                            if (newValue.length <= 4 && newValue.all { it.isDigit() }) {
                                viewModel.inputPrizeId = newValue
                            }
                        },
                        label = { Text("PrizeID") },
                        modifier = Modifier
                            .width(100.dp)
                            .offset { IntOffset(x = 45, y = -15) },
                        keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Number),
                        shape = RoundedCornerShape(24),
                        singleLine = true
                    )
                    OutlinedTextField(
                        value = viewModel.inputGameCode,
                        onValueChange = { newValue ->
                            if (newValue.length <= 19 && newValue.all { it.isDigit() }) {
                                viewModel.inputGameCode = newValue
                            }
                        },
                        label = { Text("GameCode") },
                        modifier = Modifier
                            .width(190.dp)
                            .offset { IntOffset(x = 45, y = -15) }
                            .padding(horizontal = 5.dp),
                        keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Number),
                        shape = RoundedCornerShape(24),
                        singleLine = true
                    )
                },
                floatingActionButton = {
                    FloatingActionButton(
                        onClick = {
                            focusManager.clearFocus()
                            if (viewModel.inputPrizeId.isNotEmpty() && viewModel.inputGameCode.isNotEmpty()) {
                                coroutineScope.launch {
                                    // 获取可用Token
                                    viewModel.getTokensToHelpCoupon()

                                    // 使用collectLatest监听tokens的变化，确保获取到最新结果
                                    viewModel.tokens.collectLatest { latestTokens ->

                                        if (latestTokens.isEmpty()) return@collectLatest

                                        // 加载助力URL
                                        val loadUrl = viewModel.dealWithHelpCouponUrl(
                                            latestTokens[0],
                                            viewModel.selectedShopInfo!!,
                                            viewModel.inputPrizeId,
                                            viewModel.inputGameCode
                                        )

                                        withContext(Dispatchers.Main) {
                                            helpCouponWebView.settings.userAgentString = WebUtils.generateUserAgent(latestTokens[0].appParam)
                                            helpCouponWebView.loadUrl(loadUrl)
                                            helpCouponWebView.webViewClient =
                                                object : WebViewClient() {
                                                    override fun onPageFinished(
                                                        view: WebView?,
                                                        url: String?
                                                    ) {
                                                        coroutineScope.launch {
                                                            delay(1000)
                                                            viewModel.showOperationDialog = true
                                                        }
                                                    }
                                                }
                                        }
                                        this.cancel()
                                    }
                                }
                            }
                        },
                        containerColor = BottomAppBarDefaults.bottomAppBarFabColor,
                        elevation = FloatingActionButtonDefaults.bottomAppBarFabElevation(),
                    ) {
                        Icon(
                            ImageVector.vectorResource(R.drawable.ic_start),
                            contentDescription = "开始",
                            modifier = Modifier
                                .size(width = 24.dp, height = 24.dp)
                                .offset { IntOffset(x = 5, y = 0) }
                        )
                    }
                }
            )
        }
    ) { innerPadding ->
        Box(modifier = Modifier.padding(innerPadding)) {
            // 加载进度条
            LinearProgressIndicator(
                progress = { viewModel.urlLoadProgress / 100f },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(3.dp),
            )

            // WebView
            AndroidView(
                factory = {
                    WebView(context).apply {
                        helpCouponWebView = this
                        layoutParams = ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.MATCH_PARENT
                        )

                        // 配置WebView
                        settings.apply {
                            javaScriptEnabled = true
                            // 使用WebUtils工具类生成UserAgent
                            userAgentString = WebUtils.generateUserAgent("")
                            domStorageEnabled = true
                            databaseEnabled = true
                            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                        }

                        // 设置WebChromeClient以监控加载进度
                        webChromeClient = object : WebChromeClient() {
                            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                                viewModel.urlLoadProgress = newProgress
                            }
                        }

                        // 初始加载URL
                        viewModel.loadInitialUrl(this)
                    }
                },
                modifier = Modifier.fillMaxSize()
            )

            // 操作对话框
            if (viewModel.showOperationDialog) {
                HelpCouponOperationDialog(
                    viewModel = viewModel,
                    tokens = tokens,
                    clipboardManager = clipboardManager,
                    helpCouponWebView = helpCouponWebView,
                    coroutineScope = coroutineScope
                )
            }
        }
    }
}

/**
 * 助力券操作对话框
 */
@Composable
private fun HelpCouponOperationDialog(
    viewModel: HelpCouponViewModel,
    tokens: List<TokenEntity>,
    clipboardManager: androidx.compose.ui.platform.ClipboardManager,
    helpCouponWebView: WebView,
    coroutineScope: kotlinx.coroutines.CoroutineScope
) {
    AlertDialog(
        onDismissRequest = { },
        modifier = Modifier.wrapContentSize(),
        title = { Text("助力通知") },
        text = {
            Row {
                Text("是否继续助力！")
            }
        },
        containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.8f),
        confirmButton = {
            Column {
                Button(
                    onClick = {
                        // 复制手机号到剪贴板
                        clipboardManager.setText(
                            androidx.compose.ui.text.AnnotatedString(tokens[0].phoneNumber)
                        )

                        // 标记Token状态并处理下一个Token
                        val currentToken = tokens[0]
                        markTokenAsLimited(currentToken)

                        coroutineScope.launch {
                            // 更新Token并加载下一个Token
                            handleNextToken(
                                currentToken = currentToken,
                                tokens = tokens,
                                viewModel = viewModel,
                                helpCouponWebView = helpCouponWebView,
                                loadNextUrl = true,
                                coroutineScope = coroutineScope
                            )
                        }
                    }
                ) {
                    Text("跳过/标黑")
                }
                Button(
                    onClick = {
                        // 获取当前Token
                        val currentToken = tokens[0]

                        // 更新助力券计数
                        updateHelpCouponCount(
                            currentToken,
                            viewModel.inputPrizeId,
                            viewModel.inputGameCode
                        )

                        coroutineScope.launch {
                            // 更新Token并加载下一个Token
                            handleNextToken(
                                currentToken = currentToken,
                                tokens = tokens,
                                viewModel = viewModel,
                                helpCouponWebView = helpCouponWebView,
                                loadNextUrl = true,
                                coroutineScope = coroutineScope
                            )
                        }
                    }
                ) {
                    Text("下一个/成功")
                }
            }
        },
        dismissButton = {
            Column {
                Button(
                    onClick = {
                        // 直接关闭对话框，停止助力
                        endHelpCouponSession(viewModel, helpCouponWebView)
                    }
                ) {
                    Text("结束/UN")
                }
                Button(
                    onClick = {
                        // 获取当前Token
                        val currentToken = tokens[0]

                        // 更新助力券计数
                        updateHelpCouponCount(
                            currentToken,
                            viewModel.inputPrizeId,
                            viewModel.inputGameCode
                        )

                        coroutineScope.launch {
                            // 更新Token并结束助力
                            viewModel.updateToken(currentToken)
                            endHelpCouponSession(viewModel, helpCouponWebView)
                        }
                    }
                ) {
                    Text("结束/SU")
                }
            }
        }
    )
}

/**
 * 标记Token为限制状态
 *
 * @param token 要标记的Token
 */
private fun markTokenAsLimited(token: TokenEntity) {
    token.isNew = false
    token.yhCardLimited = true
    token.activityLimited = true
}

/**
 * 更新助力券计数
 *
 * @param token 要更新的Token
 * @param prizeId 奖品ID
 * @param gameCode 游戏代码
 */
private fun updateHelpCouponCount(
    token: TokenEntity,
    prizeId: String,
    gameCode: String
) {
    val helpCouponCount = org.json.JSONObject(token.helpCouponCount)
    val sdf = java.text.SimpleDateFormat(
        "yyyy-MM-dd",
        java.util.Locale.getDefault()
    )
    val today = sdf.format(System.currentTimeMillis())

    if (helpCouponCount.has(prizeId)) {
        val helpCouponCountByPrizeId = helpCouponCount.getJSONArray(prizeId)
        helpCouponCountByPrizeId.put(0, today)
        helpCouponCountByPrizeId.put(
            1,
            helpCouponCountByPrizeId.getInt(1) + 1
        )
        helpCouponCountByPrizeId.put(
            2,
            helpCouponCountByPrizeId.getJSONArray(2)
                .put(gameCode)
        )
        helpCouponCount.put(
            prizeId,
            helpCouponCountByPrizeId
        )
    } else {
        val helpCouponCountByPrizeId =
            org.json.JSONArray().put(0, today).put(1, 1)
                .put(
                    2,
                    org.json.JSONArray()
                        .put(gameCode)
                )
        helpCouponCount.put(
            prizeId,
            helpCouponCountByPrizeId
        )
    }

    // 更新Token的helpCouponCount字段
    token.helpCouponCount = helpCouponCount.toString()
}

/**
 * 处理下一个Token
 *
 * @param currentToken 当前Token
 * @param tokens Token列表
 * @param viewModel ViewModel实例
 * @param helpCouponWebView WebView实例
 * @param loadNextUrl 是否加载下一个URL
 * @param coroutineScope 协程作用域
 */
private suspend fun handleNextToken(
    currentToken: TokenEntity,
    tokens: List<TokenEntity>,
    viewModel: HelpCouponViewModel,
    helpCouponWebView: WebView,
    loadNextUrl: Boolean,
    coroutineScope: kotlinx.coroutines.CoroutineScope
) {
    // 更新Token
    viewModel.updateToken(currentToken)

    // 获取更新后的Token列表
    val updatedTokens = viewModel.tokens.value

    if (updatedTokens.isEmpty()) {
        viewModel.helpCouponTips = "该 PrizeID 无可用 TOKEN"
        viewModel.showOperationDialog = false
    } else if (loadNextUrl) {
        viewModel.helpCouponTips = "可用 ${updatedTokens.size} 个TOKEN"
        val loadUrl = viewModel.dealWithHelpCouponUrl(
            updatedTokens[0],
            viewModel.selectedShopInfo!!,
            viewModel.inputPrizeId,
            viewModel.inputGameCode
        )
        withContext(Dispatchers.Main) {
            helpCouponWebView.settings.userAgentString = WebUtils.generateUserAgent(updatedTokens[0].appParam)
            helpCouponWebView.loadUrl(loadUrl)
            viewModel.showOperationDialog = false
        }
    }
}

/**
 * 结束助力券会话
 *
 * @param viewModel ViewModel实例
 * @param helpCouponWebView WebView实例
 */
private fun endHelpCouponSession(
    viewModel: HelpCouponViewModel,
    helpCouponWebView: WebView
) {
    viewModel.helpCouponTips = "助力结束"
    viewModel.showOperationDialog = false

    // 重置WebView客户端
    helpCouponWebView.webViewClient = object : WebViewClient() {
        override fun onPageStarted(
            view: WebView?,
            url: String?,
            favicon: Bitmap?
        ) {
        }

        override fun onPageFinished(view: WebView?, url: String?) {}
    }
}

/**
 * 助力券商店选择下拉菜单
 */
@Composable
fun HelpCouponShopSelectionMenu(
    expanded: Boolean,
    onDismissRequest: () -> Unit,
    needNewToken: Boolean,
    onNeedNewTokenChange: (Boolean) -> Unit,
    shopInfoList: List<ShopInfo>,
    selectedShopInfo: ShopInfo?,
    onShopSelected: (ShopInfo) -> Unit
) {
    DropdownMenu(
        expanded = expanded,
        onDismissRequest = onDismissRequest,
        modifier = Modifier.background(MaterialTheme.colorScheme.surface.copy(alpha = 0.8f))
    ) {
        // NEW TOKEN 开关
        Text(
            "NEW TOKEN",
            fontSize = 15.sp,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(start = 12.dp, top = 10.dp)
        )
        Switch(
            checked = needNewToken,
            onCheckedChange = onNeedNewTokenChange,
            thumbContent = {
                Icon(
                    imageVector = if (needNewToken) Icons.Filled.Check else Icons.Filled.Close,
                    contentDescription = null,
                    modifier = Modifier.size(SwitchDefaults.IconSize),
                )
            },
            colors = SwitchDefaults.colors(
                uncheckedBorderColor = MaterialTheme.colorScheme.primary,
                uncheckedTrackColor = MaterialTheme.colorScheme.primary,
                uncheckedThumbColor = MaterialTheme.colorScheme.primaryContainer,
                checkedBorderColor = MaterialTheme.colorScheme.primary,
            ),
            modifier = Modifier.padding(start = 12.dp)
        )

        // 地区选择
        Text(
            "地区:",
            fontSize = 15.sp,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(start = 12.dp, top = 10.dp)
        )

        shopInfoList.forEach { shopInfo ->
            DropdownMenuItem(
                text = {
                    Text(
                        shopInfo.cityName,
                        modifier = Modifier.padding(start = 2.dp),
                        color = if (selectedShopInfo?.cityName == shopInfo.cityName) Color.Red else Color.Unspecified
                    )
                },
                onClick = { onShopSelected(shopInfo) }
            )
        }
    }
}