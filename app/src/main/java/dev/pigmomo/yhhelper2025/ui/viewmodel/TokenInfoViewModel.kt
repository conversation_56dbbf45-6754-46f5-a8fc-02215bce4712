package dev.pigmomo.yhhelper2025.ui.viewmodel

import android.annotation.SuppressLint
import android.util.Log
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.content.Intent
import android.util.Patterns
import android.webkit.URLUtil
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import dev.pigmomo.yhhelper2025.data.model.TokenEntity
import dev.pigmomo.yhhelper2025.data.repository.TokenRepository
import dev.pigmomo.yhhelper2025.utils.WebUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import androidx.core.net.toUri
import java.util.regex.Pattern

/**
 * TokenInfoViewModel - 管理token信息界面的状态和业务逻辑
 */
class TokenInfoViewModel(private val tokenRepository: TokenRepository) : ViewModel() {
    // WebView加载进度
    var urlLoadProgress by mutableIntStateOf(0)
    
    // 标记是否是首次加载
    var isFirstLoad = mutableStateOf(true)
    
    // URL输入状态 
    private val _urlInputState = MutableStateFlow<UrlInputState>(UrlInputState.Idle)
    val urlInputState: StateFlow<UrlInputState> = _urlInputState.asStateFlow()
    
    // WebView引用，当创建后保存下来方便引用
    @SuppressLint("StaticFieldLeak")
    private var _webView: WebView? = null
    
    // 当前Token
    private var _currentToken: TokenEntity? = null
    
    /**
     * 初始化ViewModel，保存Token引用
     */
    fun initialize(token: TokenEntity) {
        _currentToken = token
    }
    
    /**
     * 获取当前WebView
     */
    fun getWebView(): WebView? = _webView
    
    /**
     * 处理URL输入
     */
    fun processUrlInput(input: String, webView: WebView, token: TokenEntity): UrlInputState {
        // 检查是否为有效URL
        if (URLUtil.isValidUrl(input)) {
            // 处理URL中的参数
            val processedUrl = processUrlParameters(input, token)
            webView.loadUrl(processedUrl)
            return UrlInputState.ValidUrl
        }
        
        // 检查是否为eventId
        if (isEventId(input)) {
            loadSpellLuckUrl(webView, input, token)
            return UrlInputState.ValidEventId
        }
        
        return UrlInputState.InvalidInput
    }
    
    /**
     * 处理URL中的参数
     * 替换token参数和其他指定参数
     */
    private fun processUrlParameters(url: String, token: TokenEntity): String {
        try {
            val uri = url.toUri()
            val uriBuilder = uri.buildUpon().clearQuery()
            val originalQueryParams = uri.queryParameterNames
            
            // 处理已有的参数
            for (param in originalQueryParams) {
                val value = uri.getQueryParameter(param) ?: ""
                
                // 对特定参数进行处理
                when (param) {
                    // 清空指定参数
                    in listOf("unionId", "opId", "userlat", "userlng", "trace_id", "span_id", "showmultiseller", "mobile") -> {
                        uriBuilder.appendQueryParameter(param, "")
                    }
                    
                    // 替换token参数
                    "token" -> {
                        if (value.contains("-")) {
                            uriBuilder.appendQueryParameter(param, "${token.userKey}-601933-${token.accessToken}")
                        } else {
                            uriBuilder.appendQueryParameter(param, value)
                        }
                    }
                    
                    // 替换设备相关参数
                    else -> {
                        val replacedValue = replaceDeviceParam(param, value, token)
                        uriBuilder.appendQueryParameter(param, replacedValue)
                    }
                }
            }
            
            return uriBuilder.build().toString()
        } catch (e: Exception) {
            Log.e("YHHelper", "Error processing URL parameters: ${e.message}")
            // 如果解析失败，回退到基本替换方法
            return url
        }
    }
    
    /**
     * 替换设备相关参数
     */
    private fun replaceDeviceParam(paramName: String, paramValue: String, token: TokenEntity): String {
        val appParamArr = token.appParam.split(",")
        
        // 如果appParamArr长度不够，直接返回原值
        if (appParamArr.size < 9) {
            return paramValue
        }
        
        return when (paramName) {
            "v" -> appParamArr[8]
            "deviceid" -> appParamArr[2]
            "userid" -> token.uid
            "memberId" -> token.uid
            "brand" -> appParamArr[7]
            "model" -> appParamArr[5]
            "os" -> "android"
            "osVersion" -> appParamArr[4]
            "screen" -> appParamArr[1]
            "jysessionid" -> WebUtils.generateRandomJySessionId()
            else -> paramValue
        }
    }
    
    /**
     * 设置WebView配置
     */
    @SuppressLint("SetJavaScriptEnabled")
    fun setupWebView(webView: WebView, token: TokenEntity) {
        _webView = webView
        
        // 配置WebView基本设置
        webView.settings.apply {
            javaScriptEnabled = true
            userAgentString = WebUtils.generateUserAgent(token.appParam)
            domStorageEnabled = true
            databaseEnabled = true
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        }

        // 设置WebChromeClient以监控加载进度
        webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                urlLoadProgress = newProgress
                if (newProgress == 100) {
                    if (isFirstLoad.value) {
                        injectLoginToken(webView, token)
                        isFirstLoad.value = false
                        loadActivityUrl(webView)
                    }
                }
            }
        }

        // 设置WebViewClient处理URL加载事件
        webView.webViewClient = object : WebViewClient() {
            @Deprecated("Deprecated in Java")
            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                if (url != null) {
                    if (url.startsWith("intent://") || url.startsWith("myyh://")) {
                        try {
                            val intent = Intent.parseUri(url, Intent.URI_INTENT_SCHEME)
                            view?.context?.startActivity(intent)
                        } catch (e: Exception) {
                            // Handle the exception
                        }
                        return true
                    }
                }
                return super.shouldOverrideUrlLoading(view, url)
            }
        }
    }

    /**
     * 注入登录token到WebView
     */
    private fun injectLoginToken(webView: WebView, token: TokenEntity) {
        val memberId = token.uid
        val userKey = token.userKey
        val javascriptCode = "localStorage.setItem('YH_LOGIN_TOKEN', '{\"data\":{\"memberId\":\"$memberId\",\"userKey\":\"$userKey\"}}')"
        webView.evaluateJavascript("javascript:(function() { $javascriptCode })()", null)
    }

    /**
     * 加载活动页面
     */
    private fun loadActivityUrl(webView: WebView) {
        val url = "https://cmsh5.yonghuivip.com/index.html?addressId=&aid=64eee3ba47bb9a000665c1c3&appType=h5&appid=VWaawuihw54Ut6YH&brand=&channel=4194304&channelSub=&cityid=3&deviceid=4bc4d057-c34f-4db1-855b-55357077049a&jysessionid=&lat=29.536294&lng=106.459936&model=&memberId=&networkType=4g&os=Win32&osVersion&platform=outsiderh5&productLine=yhStore&proportion=1.25&salesChannel=213&screen=1536%2A864&sellerid=7&shopid=9162"
        webView.loadUrl(url)
    }

    /**
     * 加载初始URL
     */
    fun loadInitialUrl(webView: WebView, tokenEntity: TokenEntity) {
        _webView = webView
        _currentToken = tokenEntity

        // 加载用户中心页面
        val url = "https://m.yonghuivip.com/yhlife/index.html#/pages/user/index"
        webView.loadUrl(url)
    }
    
    /**
     * 刷新当前页面
     */
    fun refreshPage() {
        _webView?.reload()
        
        // 检查是否在登录中间页，如果是则跳转到用户中心
        if (_webView?.url == "https://m.yonghuivip.com/yhlife/index.html#/pages/commonloginmiddle/index?mode=normal") {
            _webView?.loadUrl("https://m.yonghuivip.com/yhlife/index.html#/pages/user/index")
        }
    }
    
    /**
     * 后退
     * @return 是否成功后退
     */
    fun goBack(): Boolean {
        return if (_webView?.canGoBack() == true) {
            _webView?.goBack()
            true
        } else {
            false
        }
    }
    
    /**
     * 获取当前URL
     */
    fun getCurrentUrl(): String? = _webView?.url
    
    /**
     * 加载拼手气链接
     */
    private fun loadSpellLuckUrl(webView: WebView, eventId: String, token: TokenEntity) {
        val appParamArr = token.appParam.split(",")
        val spellLuckUrl = "https://m.yonghuivip.com/yh-m-site/yh-spell-luck/index.html?needlogin=0&canShare=1&needlocation=1&showLoading=1&activityScene=hand_luck&eventId=$eventId&isForward=0&unionId=&opId=&cityid=3&lng=&lat=&userlng=&userlat=&addressId=&appid=wxc9cf7c95499ee604&v=${appParamArr[8]}&fromorigin=miniprogram&mobile=&deviceid=${appParamArr[2]}&platform=android&userid=${token.uid}&sellerid=7&shopid=9088&sellername=永辉超市&shopname=&scDistinctId=${token.uid}&FPName=-99&PPName=-99&sceId=1007&project_name=yh_life&pub_v=19&yh_appName=永辉生活&trace_id=&span_id=&mid=WeChat&sid=Tencentfreetraffic&cid=Chat&showmultiseller=&trackPub={\"yh_openGId\":\"-99\",\"yh_isLocationAllowed\":\"1\",\"yh_sceneId\":\"1007\",\"yh_sceneName\":\"单人聊天会话中的小程序消息卡片\",\"yh_abVersion\":\"-99\",\"yh_chanShopId\":\"-99\",\"yh_roomId\":\"-99\",\"yh_weixinadinfo\":\"-99\",\"yh_chanId\":\"-99\",\"yh_userType\":\"-99\",\"yh_sharedRelationships\":\"-99\"}&token=${token.userKey + "-601933-" + token.accessToken}&brand=${appParamArr[7]}&model=${appParamArr[5]}&os=android&osVersion=${appParamArr[4]}&screen=${appParamArr[1]}&productLine=YhStore&channelSub=&appType=mini&proportion=2.75&networkType=4g&channel=512&jysessionid=${WebUtils.generateRandomJySessionId()}"
        webView.loadUrl(spellLuckUrl)
    }
    
    /**
     * 验证URL是否为19位数字（eventId）
     */
    private fun isEventId(input: String): Boolean {
        val pattern19 = "\\d{19}"
        val regex19 = Regex(pattern19)
        return regex19.matches(input)
    }
    
    override fun onCleared() {
        super.onCleared()
        _webView = null
        _currentToken = null
    }
}

/**
 * URL输入处理状态
 */
sealed class UrlInputState {
    object Idle : UrlInputState()
    object ValidUrl : UrlInputState()
    object ValidEventId : UrlInputState()
    object InvalidInput : UrlInputState()
} 