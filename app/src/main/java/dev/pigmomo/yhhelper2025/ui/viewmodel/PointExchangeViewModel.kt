package dev.pigmomo.yhhelper2025.ui.viewmodel

import android.webkit.WebView
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.ClipboardManager
import androidx.compose.ui.text.AnnotatedString
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.pigmomo.yhhelper2025.data.model.ShopDataProvider
import dev.pigmomo.yhhelper2025.data.model.ShopInfo
import dev.pigmomo.yhhelper2025.data.model.TokenEntity
import dev.pigmomo.yhhelper2025.data.repository.TokenRepository
import dev.pigmomo.yhhelper2025.utils.UrlParamUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.UUID
import kotlin.random.Random

/**
 * PointExchangeViewModel - 管理积分组队界面的状态和业务逻辑
 */
class PointExchangeViewModel(private val tokenRepository: TokenRepository) : ViewModel() {
    // 状态管理
    var pointExchangeTips by mutableStateOf("")
    var settingExpanded by mutableStateOf(false)
    var needNewToken by mutableStateOf(false)
    var showOperationDialog by mutableStateOf(false)
    var isStarted by mutableStateOf(false)
    
    // 输入字段
    var inputTeamCode by mutableStateOf("")
    
    // 商店信息
    private val _shopInfoList = MutableStateFlow<List<ShopInfo>>(ShopDataProvider.getShopInfoList())
    val shopInfoList: StateFlow<List<ShopInfo>> = _shopInfoList.asStateFlow()
    
    var selectedShopInfo by mutableStateOf<ShopInfo?>(null)
    
    // 可用Token列表
    private val _tokens = MutableStateFlow<List<TokenEntity>>(emptyList())
    val tokens: StateFlow<List<TokenEntity>> = _tokens.asStateFlow()
    
    // WebView加载进度
    var urlLoadProgress by mutableIntStateOf(0)
    
    init {
        // 初始化选中的商店信息
        viewModelScope.launch {
            _shopInfoList.value.firstOrNull()?.let {
                selectedShopInfo = it
            }
        }
    }
    
    /**
     * 从参数中提取TeamCode
     * 
     * @param param 可能包含TeamCode的参数
     * @return 是否成功设置了TeamCode
     */
    fun processKeyParam(param: String?): Boolean {
        val teamCode = UrlParamUtils.extractTeamCode(param) ?: return false
        inputTeamCode = teamCode
        return true
    }
    
    /**
     * 从剪贴板提取TeamCode
     * 
     * @param clipboardManager 剪贴板管理器
     * @return 是否成功设置了TeamCode
     */
    fun processClipboardContent(clipboardManager: ClipboardManager): Boolean {
        val clipboardContent = clipboardManager.getText()?.toString() ?: return false
        val teamCode = UrlParamUtils.extractTeamCode(clipboardContent) ?: return false
        inputTeamCode = teamCode
        return true
    }
    
    /**
     * 获取可用于积分组队的Token列表
     */
    fun getTokensToPointExchange() {
        if (inputTeamCode.isEmpty() || inputTeamCode.length < 16) {
            pointExchangeTips = "请输入正确的TeamCode"
            return
        }
        
        viewModelScope.launch {
            val tokenList = tokenRepository.getTokensToPointExchange(inputTeamCode, needNewToken)
            _tokens.value = tokenList

            pointExchangeTips = if (tokenList.isEmpty()) {
                isStarted = false
                "该 TeamCode 无可用 TOKEN"
            } else {
                "可用 ${tokenList.size} 个TOKEN"
            }
        }
    }
    
    /**
     * 生成随机的会话ID
     */
    fun generateRandomJySessionId(): String {
        val uuid = UUID.randomUUID().toString().replace("-", "")
        val random = Random.nextInt(100000, 999999)
        return "$uuid$random"
    }
    
    /**
     * 处理积分组队URL
     */
    fun dealWithPointExchangeUrl(
        token: TokenEntity,
        switchedShopInfo: ShopInfo,
        teamCode: String
    ): String {
        val appParamArr = token.appParam.split(",")
        return "https://m.yonghuivip.com/yh-m-site/yh-point-exchange/index.html?needlocation=1&canShare=1&ngbcolor=FFE0D1&nfcolor=000000&teamCode=$teamCode&mid=jfsc&sid=VIP&cid=zdgf&isForward=0&unionId=&opId=&cityid=${switchedShopInfo.cityId}&lng=${switchedShopInfo.lng}&lat=${switchedShopInfo.lat}&userlng=&userlat=&addressId=&appid=wxc9cf7c95499ee604&v=${appParamArr[8]}&fromorigin=miniprogram&mobile=&deviceid=${appParamArr[2]}&platform=android&userid=${token.uid}&sellerid=${switchedShopInfo.sellerId}&shopid=${switchedShopInfo.shopId}&sellername=永辉超市&shopname=${switchedShopInfo.shopName}&scDistinctId=${token.uid}&FPName=-99&PPName=-99&sceId=1089&project_name=yh_life&pub_v=19&yh_appName=永辉生活&trace_id=&span_id=&showmultiseller={\"${switchedShopInfo.sellerId}\":\"${switchedShopInfo.shopId}\"}&trackPub={\"yh_openGId\":\"-99\",\"yh_isLocationAllowed\":\"1\",\"yh_sceneId\":\"1089\",\"yh_sceneName\":\"微信聊天主界面下拉\",\"yh_abVersion\":\"-99\",\"yh_chanShopId\":\"-99\",\"yh_roomId\":\"-99\",\"yh_weixinadinfo\":\"-99\",\"yh_chanId\":\"-99\",\"yh_userType\":\"-99\",\"yh_sharedRelationships\":\"-99\"}&token=${token.userKey + "-601933-" + token.accessToken}&brand=${appParamArr[7]}&model=${appParamArr[5]}&os=android&osVersion=${appParamArr[4]}&screen=${appParamArr[1]}&productLine=YhStore&channelSub=&appType=mini&proportion=2.75&networkType=${appParamArr[6]}&channel=512&jysessionid=${generateRandomJySessionId()}"
    }
    
    /**
     * 加载初始URL
     */
    fun loadInitialUrl(webView: WebView) {
        selectedShopInfo?.let { shopInfo ->
            val url = "https://m.yonghuivip.com/yh-m-site/yh-point-exchange/index.html?needlocation=1&canShare=1&ngbcolor=FFE0D1&nfcolor=000000&isForward=0&unionId=oCjU7wEniZNxUyJV2eTzJETwx1h0&opId=oP_Ic0Zm3vu6JbtcY-_wevQ3pzKQ&cityid=${shopInfo.cityId}&lng=${shopInfo.lng}&lat=${shopInfo.lat}&userlng=&userlat=&addressId=&appid=wxc9cf7c95499ee604&v=*********&fromorigin=miniprogram&mobile=15111802414&deviceid=3a4058f8-3e5c-41be-968f-3508988f5ec8&platform=android&userid=694319091981073469&sellerid=${shopInfo.sellerId}&shopid=${shopInfo.shopId}&sellername=%E6%B0%B8%E8%BE%89%E8%B6%85%E5%B8%82&shopname=${shopInfo.shopName}&scDistinctId=694319091981073469&FPName=%E6%88%91%E7%9A%84%E9%A1%B5&PPName=%E6%88%91%E7%9A%84%E9%A1%B5&sceId=1089&project_name=yh_life&pub_v=19&yh_appName=%E6%B0%B8%E8%BE%89%E7%94%9F%E6%B4%BB&trace_id=&span_id=&mid=WeChat&sid=Tencentfreetraffic&cid=Taskbar&showmultiseller=%7B%227%22%3A%229162%22%7D&trackPub=%7B%22yh_openGId%22%3A%22-99%22%2C%22yh_isLocationAllowed%22%3A%221%22%2C%22yh_sceneId%22%3A%221089%22%2C%22yh_sceneName%22%3A%22%E5%BE%AE%E4%BF%A1%E8%81%8A%E5%A4%A9%E4%B8%BB%E7%95%8C%E9%9D%A2%E4%B8%8B%E6%8B%89%22%2C%22yh_abVersion%22%3A%22-99%22%2C%22yh_chanShopId%22%3A%22-99%22%2C%22yh_roomId%22%3A%22-99%22%2C%22yh_weixinadinfo%22%3A%22-99%22%2C%22yh_chanId%22%3A%22-99%22%2C%22yh_userType%22%3A%22-99%22%2C%22yh_sharedRelationships%22%3A%22-99%22%7D&token=34ec3fa07353904dd02672fcb31b3be3a1f4ed0e99fa13820b35fd71983654c1ee69fc3fcd53df5d912daa46b4dfbe9e51f7695fb5c564705b071a6a7a426c35661fc8c76dc281fa2f9066dc6b59c11c-601933-34ec3fa07353904dd02672fcb31b3be3a1f4ed0e99fa13820b35fd71983654c1ee69fc3fcd53df5d912daa46b4dfbe9e51f7695fb5c564705b071a6a7a426c35661fc8c76dc281fa2f9066dc6b59c11c&brand=Xiaomi&model=Redmi%20K20%20Pro&os=android&osVersion=Android%2012&screen=1080.75*2340.25&productLine=YhStore&channelSub=&appType=mini&proportion=2.75&networkType=wifi&channel=512&jysessionid=${generateRandomJySessionId()}"
            webView.loadUrl(url)
        }
    }
    
    /**
     * 更新Token信息
     * 
     * @param token 要更新的Token实体
     */
    suspend fun updateToken(token: TokenEntity) {
        // 更新Token到数据库
        tokenRepository.updateToken(token)

        // 直接从列表中移除已使用的Token
        _tokens.value = _tokens.value.filter { it.uid != token.uid }
    }
    
    /**
     * 更新积分组队计数
     * 
     * @param token 要更新的Token
     * @param teamCode teamCode
     */
    fun updatePointExchangeCount(token: TokenEntity, teamCode: String) {
        val pointExchangeArr = JSONArray(token.pointExchangeCount)
        val lastHelpTimeStr = pointExchangeArr.getString(0)
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val today = sdf.format(System.currentTimeMillis())
        
        if (lastHelpTimeStr!= today) {
            val pointExchangeData = pointExchangeArr.getJSONArray(2)
            pointExchangeArr.put(0, today) // 更新日期
            pointExchangeArr.put(1, pointExchangeArr.getInt(1) + 1) // 增加计数
            pointExchangeArr.put(2, pointExchangeData.put(teamCode)) // 添加teamCode记录
        }
        
        // 更新Token的pointExchangeCount字段
        token.pointExchangeCount = pointExchangeArr.toString()
    }
} 