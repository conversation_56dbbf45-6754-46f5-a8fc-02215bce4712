package dev.pigmomo.yhhelper2025.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.ClipboardManager
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import dev.pigmomo.yhhelper2025.data.model.TokenEntity

/**
 * 操作选择对话框
 *
 * @param token 当前操作的令牌
 * @param onDismiss 取消回调
 * @param onNoteClick 备注按钮点击回调
 * @param onCopyClick 复制按钮点击回调
 * @param onMarkOldClick 标记为旧按钮点击回调
 * @param onMarkLimitedClick 标记为黑号按钮点击回调
 * @param onDeleteClick 删除按钮点击回调
 * @param onResetClick 重置按钮点击回调
 */
@Composable
fun TokenActionDialog(
    token: TokenEntity,
    onDismiss: () -> Unit,
    onNoteClick: () -> Unit,
    onCopyClick: () -> Unit,
    onMarkOldClick: () -> Unit,
    onMarkLimitedClick: () -> Unit,
    onDeleteClick: () -> Unit,
    onResetClick: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MaterialTheme.colorScheme.surfaceContainer,
        title = { Text("操作 ${token.phoneNumber}") },
        text = {
            Column {
                listOf(
                    "备注" to onNoteClick,
                    "复制" to onCopyClick,
                    "标老" to onMarkOldClick,
                    "标黑" to onMarkLimitedClick,
                    "删除" to onDeleteClick,
                    "重置" to onResetClick
                ).forEach { (text, action) ->
                    TextButton(
                        onClick = { 
                            onDismiss()
                            action() 
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(text)
                    }
                }
            }
        },
        confirmButton = {},
        dismissButton = {}
    )
}

/**
 * 备注编辑对话框
 *
 * @param initialNote 初始备注内容
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调，返回新的备注内容
 * @param clipboardManager 剪贴板管理器
 */
@Composable
fun NoteEditDialog(
    initialNote: String,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit,
    clipboardManager: ClipboardManager
) {
    val focusRequester = remember { FocusRequester() }
    val noteTextFieldValue = remember { mutableStateOf(TextFieldValue(initialNote)) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MaterialTheme.colorScheme.surfaceContainer,
        title = { Text(text = "输入备注") },
        text = {
            OutlinedTextField(
                value = noteTextFieldValue.value,
                onValueChange = { noteTextFieldValue.value = it },
                modifier = Modifier.focusRequester(focusRequester),
                label = { Text("NOTE") },
            )
        },
        confirmButton = {
            Button(onClick = { onConfirm(noteTextFieldValue.value.text) }) {
                Text("确认")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
            Button(
                onClick = {
                    val clipboardText = clipboardManager.getText()
                    if (clipboardText != null) {
                        noteTextFieldValue.value = noteTextFieldValue.value.copy(
                            text = noteTextFieldValue.value.text + clipboardText.text,
                            selection = TextRange(noteTextFieldValue.value.text.length + clipboardText.text.length)
                        )
                    }
                }
            ) {
                Text("粘贴")
            }
        }
    )

    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }

    DisposableEffect(Unit) {
        noteTextFieldValue.value = noteTextFieldValue.value.copy(
            selection = TextRange(noteTextFieldValue.value.text.length)
        )
        onDispose { }
    }
}

/**
 * 删除确认对话框
 *
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调
 */
@Composable
fun DeleteConfirmDialog(
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MaterialTheme.colorScheme.surfaceContainer,
        title = { Text(text = "确认删除？") },
        text = {
            Text(
                text = "删除后不可恢复！！！",
                modifier = Modifier.padding(bottom = 10.dp)
            )
        },
        confirmButton = {
            Button(onClick = onConfirm) {
                Text("确认/复制")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 重置确认对话框
 *
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调
 */
@Composable
fun ResetConfirmDialog(
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MaterialTheme.colorScheme.surfaceContainer,
        title = { Text(text = "确认重置？") },
        text = {
            Text(
                text = "重置后不可恢复！！！",
                modifier = Modifier.padding(bottom = 10.dp)
            )
        },
        confirmButton = {
            Button(onClick = onConfirm) {
                Text("确认")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}