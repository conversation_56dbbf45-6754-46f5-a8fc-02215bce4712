package dev.pigmomo.yhhelper2025.ui

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.ArrowBack
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.lifecycle.ViewModelProvider
import dev.pigmomo.yhhelper2025.ui.components.SettingButton
import dev.pigmomo.yhhelper2025.ui.theme.Yhhelper2025Theme
import dev.pigmomo.yhhelper2025.ui.viewmodel.SettingViewModel
import dev.pigmomo.yhhelper2025.ui.viewmodel.ViewModelFactory
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class SettingActivity : ComponentActivity() {

    // ViewModel
    private lateinit var viewModel: SettingViewModel

    // 对话框状态
    var showAccountExportDialog by mutableStateOf(false)
    var showBackupExportDialog by mutableStateOf(false)
    var showMatchExportDialog by mutableStateOf(false)

    // 账号导入文件选择器
    private val accountImportFileLauncher = registerForActivityResult(
        ActivityResultContracts.OpenDocument()
    ) { uri ->
        uri?.let { viewModel.importAccounts(it) }
    }

    // 备份文件选择器
    private val backupFileLauncher = registerForActivityResult(
        ActivityResultContracts.CreateDocument("application/octet-stream")
    ) { uri ->
        uri?.let { viewModel.backupDatabase(it) }
    }

    // 恢复文件选择器
    private val restoreFileLauncher = registerForActivityResult(
        ActivityResultContracts.OpenDocument()
    ) { uri ->
        uri?.let { viewModel.restoreDatabase(it) }
    }

    // 账号导出文件选择器
    private val accountExportFileLauncher = registerForActivityResult(
        ActivityResultContracts.CreateDocument("text/plain")
    ) { uri ->
        uri?.let {
            viewModel.exportAccountsToUri(it, accountExportStartIndex, accountExportEndIndex)
        }
    }

    // 备份导出文件选择器
    private val backupExportFileLauncher = registerForActivityResult(
        ActivityResultContracts.CreateDocument("text/plain")
    ) { uri ->
        uri?.let {
            viewModel.exportBackupToUri(it, backupExportStartIndex, backupExportEndIndex)
        }
    }

    // 匹配导出文件选择器
    private val matchExportFileLauncher = registerForActivityResult(
        ActivityResultContracts.CreateDocument("text/plain")
    ) { uri ->
        uri?.let {
            viewModel.exportMatchedAccountsToUri(it, matchExportKeyword, matchExportMaxCount, matchExportIsExactMatch)
        }
    }

    // 保存导出范围
    private var accountExportStartIndex: Int = 0
    private var accountExportEndIndex: Int = 0
    private var backupExportStartIndex: Int = 0
    private var backupExportEndIndex: Int = 0

    // 保存匹配导出参数
    private var matchExportKeyword: String = ""
    private var matchExportMaxCount: Int = 0
    private var matchExportIsExactMatch: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化ViewModel
        viewModel = ViewModelProvider(
            this,
            ViewModelFactory.getInstance(applicationContext)
        )[SettingViewModel::class.java]

        setContent {
            Yhhelper2025Theme(dynamicColor = false) {
                SettingScreen(this)
            }
        }
    }

    // 启动备份过程
    fun startBackup() {
        val timeStamp = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault()).format(Date())
        backupFileLauncher.launch("backup$timeStamp.db")
    }

    // 启动恢复过程
    fun startRestore() {
        restoreFileLauncher.launch(arrayOf("application/octet-stream"))
    }

    // 启动账号导入过程
    fun startImportAccounts() {
        accountImportFileLauncher.launch(arrayOf("text/plain"))
    }

    // 导出账号（导出后删除）- 启动导出过程
    fun exportAccounts(startIndex: Int, endIndex: Int) {
        // 保存导出范围
        accountExportStartIndex = startIndex
        accountExportEndIndex = endIndex

        // 启动文件选择器
        val timeStamp = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault()).format(Date())
        accountExportFileLauncher.launch("accounts${timeStamp}.txt")

        // 隐藏对话框
        showAccountExportDialog = false
    }

    // 备份导出（不删除数据）- 启动导出过程
    fun exportBackup(startIndex: Int, endIndex: Int) {
        // 保存导出范围
        backupExportStartIndex = startIndex
        backupExportEndIndex = endIndex

        // 启动文件选择器
        val timeStamp = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault()).format(Date())
        backupExportFileLauncher.launch("backup${timeStamp}.txt")

        // 隐藏对话框
        showBackupExportDialog = false
    }

    // 匹配导出（导出后删除）- 启动导出过程
    fun exportMatchedAccounts(keyword: String, maxCount: Int, isExactMatch: Boolean) {
        // 保存匹配导出参数
        matchExportKeyword = keyword
        matchExportMaxCount = maxCount
        matchExportIsExactMatch = isExactMatch

        // 启动文件选择器
        val timeStamp = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault()).format(Date())
        matchExportFileLauncher.launch("matched_accounts${timeStamp}.txt")

        // 隐藏对话框
        showMatchExportDialog = false
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SettingScreen(activity: SettingActivity) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val viewModel = ViewModelProvider(
        activity,
        ViewModelFactory.getInstance(context)
    )[SettingViewModel::class.java]

    // 收集状态
    val recovering by viewModel.recovering.collectAsState()
    val backup by viewModel.backup.collectAsState()
    val sorted by viewModel.sorted.collectAsState()
    val inputting by viewModel.inputting.collectAsState()
    val accountExporting by viewModel.accountExporting.collectAsState()
    val backupExporting by viewModel.backupExporting.collectAsState()
    val matchExporting by viewModel.matchExporting.collectAsState()
    val operationResult by viewModel.operationResult.collectAsState()

    // 处理操作结果
    LaunchedEffect(operationResult) {
        operationResult?.let {
            when (it) {
                is SettingViewModel.OperationResult.Success -> {
                    Toast.makeText(context, it.message, Toast.LENGTH_SHORT).show()
                }
                is SettingViewModel.OperationResult.Error -> {
                    Toast.makeText(context, it.message, Toast.LENGTH_SHORT).show()
                }
            }
            viewModel.clearOperationResult()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Row(verticalAlignment = Alignment.Bottom) {
                        Text("设置", maxLines = 1)
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    actionIconContentColor = Color.White
                ),
                navigationIcon = {
                    IconButton(onClick = { activity.finish() }) {
                        Icon(
                            imageVector = Icons.Outlined.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .padding(
                    top = innerPadding.calculateTopPadding() + 20.dp,
                    bottom = innerPadding.calculateBottomPadding() + 10.dp,
                    start = 10.dp,
                    end = 10.dp
                )
                .verticalScroll(rememberScrollState())
                .background(
                    MaterialTheme.colorScheme.surface,
                    shape = RoundedCornerShape(10.dp)
                )
        ) {
            Text(text = "备份与恢复", modifier = Modifier.padding(start = 20.dp, top = 10.dp))
            HorizontalDivider(modifier = Modifier.padding(start = 20.dp, end = 20.dp))
            
            // 数据库恢复按钮
            SettingButton(
                text = "数据库恢复",
                loadingText = "正在导入数据库...",
                isLoading = recovering,
                onClick = { activity.startRestore() },
                paddingValues = Modifier.padding(start = 20.dp, end = 20.dp, top = 5.dp)
            )
            
            HorizontalDivider(
                modifier = Modifier.padding(
                    top = 5.dp,
                    bottom = 5.dp,
                    start = 20.dp,
                    end = 20.dp
                )
            )
            
            // 数据库备份按钮
            SettingButton(
                text = "数据库备份",
                loadingText = "正在备份数据库...",
                isLoading = backup,
                onClick = { 
                    coroutineScope.launch {
                        activity.startBackup()
                    }
                },
                paddingValues = Modifier.padding(start = 20.dp, end = 20.dp, bottom = 10.dp)
            )

            Text(text = "数据库整理", modifier = Modifier.padding(start = 20.dp, top = 0.dp))
            HorizontalDivider(modifier = Modifier.padding(start = 20.dp, end = 20.dp))
            
            // 数据库整理按钮
            SettingButton(
                text = "数据库整理",
                loadingText = "正在整理数据库...",
                isLoading = sorted,
                onClick = { 
                    coroutineScope.launch {
                        viewModel.sortDatabase()
                    }
                },
                paddingValues = Modifier.padding(start = 20.dp, top = 5.dp, end = 20.dp, bottom = 10.dp)
            )

            Text(text = "导入与导出", modifier = Modifier.padding(start = 20.dp, top = 0.dp))
            HorizontalDivider(modifier = Modifier.padding(start = 20.dp, end = 20.dp))
            
            // 账号导入按钮
            SettingButton(
                text = "账号导入",
                loadingText = "账号导入中...",
                isLoading = inputting,
                onClick = { activity.startImportAccounts() },
                paddingValues = Modifier.padding(start = 20.dp, end = 20.dp, top = 5.dp)
            )
            
            HorizontalDivider(
                modifier = Modifier.padding(
                    top = 5.dp,
                    bottom = 5.dp,
                    start = 20.dp,
                    end = 20.dp
                )
            )
            
            // 账号导出按钮
            SettingButton(
                text = "账号导出",
                loadingText = "账号导出中...",
                isLoading = accountExporting,
                onClick = { activity.showAccountExportDialog = true }
            )

            // 账号导出对话框
            if (activity.showAccountExportDialog) {
                ExportDialog(
                    title = "导出账号",
                    onDismiss = { activity.showAccountExportDialog = false },
                    onConfirm = { startIndex, endIndex ->
                        coroutineScope.launch {
                            activity.exportAccounts(startIndex, endIndex)
                        }
                    },
                    activity = activity
                )
            }
            
            HorizontalDivider(
                modifier = Modifier.padding(
                    top = 5.dp,
                    bottom = 5.dp,
                    start = 20.dp,
                    end = 20.dp
                )
            )

            // 匹配导出按钮
            SettingButton(
                text = "匹配导出",
                loadingText = "匹配导出中...",
                isLoading = matchExporting,
                onClick = { activity.showMatchExportDialog = true }
            )

            // 匹配导出对话框
            if (activity.showMatchExportDialog) {
                MatchExportDialog(
                    onDismiss = { activity.showMatchExportDialog = false },
                    onConfirm = { keyword, maxCount, isExactMatch ->
                        coroutineScope.launch {
                            activity.exportMatchedAccounts(keyword, maxCount, isExactMatch)
                        }
                    },
                    activity = activity
                )
            }

            HorizontalDivider(
                modifier = Modifier.padding(
                    top = 5.dp,
                    bottom = 5.dp,
                    start = 20.dp,
                    end = 20.dp
                )
            )

            // 备份导出按钮
            SettingButton(
                text = "备份导出",
                loadingText = "备份导出中...",
                isLoading = backupExporting,
                onClick = { activity.showBackupExportDialog = true },
                paddingValues = Modifier.padding(start = 20.dp, end = 20.dp, bottom = 10.dp)
            )

            // 备份导出对话框
            if (activity.showBackupExportDialog) {
                ExportDialog(
                    title = "备份导出",
                    onDismiss = { activity.showBackupExportDialog = false },
                    onConfirm = { startIndex, endIndex ->
                        coroutineScope.launch {
                            activity.exportBackup(startIndex, endIndex)
                        }
                    },
                    activity = activity
                )
            }

            Text(text = "权限管理", modifier = Modifier.padding(start = 20.dp, top = 0.dp))
            HorizontalDivider(modifier = Modifier.padding(start = 20.dp, end = 20.dp))
            //打开权限管理页面
            Button(
                modifier = Modifier
                    .padding(start = 20.dp, top = 5.dp, end = 20.dp, bottom = 10.dp)
                    .height(50.dp)
                    .fillMaxWidth(),
                onClick = {
                    val intent = Intent()
                    intent.action = "android.settings.APPLICATION_DETAILS_SETTINGS"
                    intent.data = ("package:" + context.packageName).toUri()
                    context.startActivity(intent)
                },
                shape = RoundedCornerShape(8.dp),
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    Text("权限管理")
                    Text(
                        text = "自启动、剪切板、通知、省电策略",
                        fontSize = 8.sp,
                        color = Color.White.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

/**
 * 导出对话框
 *
 * @param title 对话框标题
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调，返回起始位置和终止位置
 */
@Composable
fun ExportDialog(
    title: String,
    onDismiss: () -> Unit,
    onConfirm: (Int, Int) -> Unit,
    activity: SettingActivity
) {
    var startIndex by remember { mutableStateOf("") }
    var endIndex by remember { mutableStateOf("") }
    
    // 计算将要导出的数量
    val exportCount = remember(startIndex, endIndex) {
        if (startIndex.isNotEmpty() && endIndex.isNotEmpty()) {
            try {
                val start = startIndex.toInt()
                val end = endIndex.toInt()
                if (start > 0 && end >= start) {
                    end - start + 1
                } else {
                    0
                }
            } catch (e: NumberFormatException) {
                0
            }
        } else {
            0
        }
    }

    AlertDialog(
        onDismissRequest = { onDismiss() },
        title = { Text(title + if (exportCount > 0) " $exportCount 个" else "") },
        containerColor = MaterialTheme.colorScheme.surfaceContainer,
        text = {
            Column {
                OutlinedTextField(
                    value = startIndex,
                    onValueChange = {
                        if (it.all { char -> char.isDigit() } && it.length <= 5) {
                            startIndex = it
                        }
                    },
                    label = { Text("起始位置") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                )
                OutlinedTextField(
                    value = endIndex,
                    onValueChange = {
                        if (it.all { char -> char.isDigit() } && it.length <= 5) {
                            endIndex = it
                        }
                    },
                    label = { Text("终止位置") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (startIndex.isEmpty() || endIndex.isEmpty()) {
                        Toast.makeText(
                            activity,
                            "请输入起始位置和终止位置",
                            Toast.LENGTH_SHORT
                        ).show()
                        return@Button
                    }
                    if (startIndex.toInt() < 0 || endIndex.toInt() < 0) {
                        Toast.makeText(
                            activity,
                            "请输入正确的起始位置和终止位置",
                            Toast.LENGTH_SHORT
                        ).show()
                        return@Button
                    }
                    if (startIndex.toInt() > endIndex.toInt()) {
                        Toast.makeText(
                            activity,
                            "请输入正确的起始位置和终止位置",
                            Toast.LENGTH_SHORT
                        ).show()
                        return@Button
                    }
                    onConfirm(startIndex.toInt(), endIndex.toInt())
                }
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            Button(
                onClick = { onDismiss() }
            ) {
                Text("取消")
            }
        }
    )
}

/**
 * 匹配导出对话框
 *
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调，返回关键词、匹配数量、是否全匹配
 * @param activity SettingActivity实例
 */
@Composable
fun MatchExportDialog(
    onDismiss: () -> Unit,
    onConfirm: (String, Int, Boolean) -> Unit,
    activity: SettingActivity
) {
    var keyword by remember { mutableStateOf("") }
    var maxCountStr by remember { mutableStateOf("") }
    var isExactMatch by remember { mutableStateOf(false) }

    // 计算匹配数量（这里只是预估，实际匹配在ViewModel中进行）
    val maxCount = remember(maxCountStr) {
        if (maxCountStr.isNotEmpty()) {
            try {
                maxCountStr.toInt()
            } catch (e: NumberFormatException) {
                0
            }
        } else {
            0
        }
    }

    AlertDialog(
        onDismissRequest = { onDismiss() },
        title = {
            Text("匹配导出" + if (keyword.isNotEmpty()) " - 关键词: $keyword" else "")
        },
        containerColor = MaterialTheme.colorScheme.surfaceContainer,
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                OutlinedTextField(
                    value = keyword,
                    onValueChange = { keyword = it },
                    label = { Text("匹配关键词") },
                    placeholder = { Text("输入要匹配的备注关键词") },
                    modifier = Modifier.fillMaxWidth()
                )

                OutlinedTextField(
                    value = maxCountStr,
                    onValueChange = {
                        if (it.all { char -> char.isDigit() } && it.length <= 5) {
                            maxCountStr = it
                        }
                    },
                    label = { Text("匹配数量") },
                    placeholder = { Text("输入最大导出数量，0表示不限制") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.fillMaxWidth()
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("全匹配模式")
                    Switch(
                        checked = isExactMatch,
                        onCheckedChange = { isExactMatch = it }
                    )
                }

                Text(
                    text = if (isExactMatch) "精确匹配：备注完全等于关键词" else "模糊匹配：备注包含关键词",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (keyword.isEmpty()) {
                        Toast.makeText(
                            activity,
                            "请输入匹配关键词",
                            Toast.LENGTH_SHORT
                        ).show()
                        return@Button
                    }
                    onConfirm(keyword, maxCount, isExactMatch)
                }
            ) {
                Text("确认导出")
            }
        },
        dismissButton = {
            Button(
                onClick = { onDismiss() }
            ) {
                Text("取消")
            }
        }
    )
}