package dev.pigmomo.yhhelper2025.ui

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.ViewGroup
import android.webkit.WebView
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.outlined.ArrowBack
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.outlined.Refresh
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.ViewModelProvider
import dev.pigmomo.yhhelper2025.R
import dev.pigmomo.yhhelper2025.data.model.TokenEntity
import dev.pigmomo.yhhelper2025.ui.theme.Yhhelper2025Theme
import dev.pigmomo.yhhelper2025.ui.viewmodel.TokenInfoViewModel
import dev.pigmomo.yhhelper2025.ui.viewmodel.UrlInputState
import dev.pigmomo.yhhelper2025.ui.viewmodel.ViewModelFactory
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Token信息活动界面
 */
class TokenInfoActivity : ComponentActivity() {

    lateinit var tokenInfoWebView: WebView
    private lateinit var viewModel: TokenInfoViewModel
    private lateinit var token: TokenEntity

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val tokenStr = intent.getStringExtra("token")
        if (tokenStr.isNullOrEmpty()) {
            finish()
            return
        }
        
        TokenEntity.fromString(tokenStr)?.let {
            token = it 
        } ?: run {
            finish()
            return
        }

        // 初始化ViewModel
        viewModel = ViewModelProvider(
            this,
            ViewModelFactory.getInstance(this)
        )[TokenInfoViewModel::class.java].apply {
            initialize(token)
        }

        setContent {
            Yhhelper2025Theme(dynamicColor = false) {
                Surface {
                    TokenInfoScreen(
                        activity = this@TokenInfoActivity,
                        viewModel = viewModel,
                        token = token
                    )
                }
            }
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        // 使用ViewModel处理返回逻辑
        if (!viewModel.goBack()) {
            super.onBackPressed()
            finish()
        }
    }
}

/**
 * TokenInfo屏幕组件
 */
@SuppressLint("SetJavaScriptEnabled")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TokenInfoScreen(
    activity: TokenInfoActivity,
    viewModel: TokenInfoViewModel,
    token: TokenEntity
) {
    val context = LocalContext.current
    val clipboardManager = LocalClipboardManager.current
    val focusManager = LocalFocusManager.current
    val coroutineScope = rememberCoroutineScope()

    // URL输入状态
    var isUrlInputVisible by remember { mutableStateOf(false) }
    var urlInputText by remember { mutableStateOf("") }
    var isUrlInputFocused by remember { mutableStateOf(false) }
    val urlInputFocusRequester = remember { FocusRequester() }

    // 保存WebView引用，组件销毁时清理资源
    DisposableEffect(Unit) {
        onDispose {
            viewModel.getWebView()?.destroy()
        }
    }

    Scaffold(
        modifier = Modifier.imePadding(),
        topBar = {
            TopAppBar(
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    actionIconContentColor = Color.White
                ),
                title = {
                    Row {
                        Text("信息", maxLines = 1)
                        Text(
                            token.phoneNumber,
                            fontSize = 10.sp,
                            maxLines = 1,
                            modifier = Modifier
                                .padding(start = 5.dp)
                                .offset(y = (5).dp)
                        )
                    }
                },
                navigationIcon = {
                    IconButton(onClick = { activity.finish() }) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Outlined.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                actions = {
                    // 复制当前网址按钮
                    IconButton(onClick = {
                        clipboardManager.setText(AnnotatedString(viewModel.getCurrentUrl() ?: ""))
                        Toast.makeText(context, "已复制", Toast.LENGTH_SHORT).show()
                    }) {
                        Icon(
                            imageVector = ImageVector.vectorResource(R.drawable.baseline_content_copy_24),
                            contentDescription = "复制网址",
                            modifier = Modifier
                                .width(20.dp)
                                .height(20.dp),
                            tint = Color.White
                        )
                    }

                    // 刷新页面按钮
                    IconButton(onClick = { viewModel.refreshPage() }) {
                        Icon(
                            imageVector = Icons.Outlined.Refresh,
                            contentDescription = "刷新页面",
                            tint = Color.White
                        )
                    }

                    // 输入链接按钮
                    IconButton(onClick = {
                        isUrlInputVisible = !isUrlInputVisible
                        urlInputText = ""
                    }) {
                        Icon(
                            imageVector = Icons.Filled.Search,
                            contentDescription = "输入链接",
                            tint = Color.White
                        )
                    }
                }
            )
        },
        content = { innerPadding ->
            Box(modifier = Modifier.padding(innerPadding)) {
                // 加载进度条
                LinearProgressIndicator(
                    progress = { viewModel.urlLoadProgress / 100f },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(3.dp),
                )

                // WebView
                AndroidView(
                    factory = {
                        WebView(context).apply {
                            activity.tokenInfoWebView = this
                            layoutParams = ViewGroup.LayoutParams(
                                ViewGroup.LayoutParams.MATCH_PARENT,
                                ViewGroup.LayoutParams.MATCH_PARENT
                            )

                            // 配置WebView
                            viewModel.setupWebView(this, token)
                        }
                    },
                    modifier = Modifier.fillMaxSize()
                )
                LaunchedEffect(Unit) {
                    delay(250)
                    viewModel.loadInitialUrl(activity.tokenInfoWebView, token)
                }

                // URL输入框
                AnimatedVisibility(
                    visible = isUrlInputVisible,
                    enter = slideInVertically(initialOffsetY = { -it }),
                    exit = slideOutVertically(targetOffsetY = { -it })
                ) {
                    LaunchedEffect(Unit) {
                        urlInputFocusRequester.requestFocus()
                    }

                    OutlinedTextField(
                        value = urlInputText,
                        onValueChange = { urlInputText = it },
                        label = { Text("url") },
                        placeholder = {},
                        singleLine = true,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = 20.dp, end = 20.dp, top = 10.dp, bottom = 0.dp)
                            .focusRequester(urlInputFocusRequester)
                            .onFocusChanged { focusState ->
                                isUrlInputFocused = focusState.isFocused
                                if (!isUrlInputFocused) {
                                    coroutineScope.launch {
                                        kotlinx.coroutines.delay(3000)
                                        if (!isUrlInputFocused) {
                                            isUrlInputVisible = false
                                        }
                                    }
                                }
                            },
                        shape = RoundedCornerShape(24),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedContainerColor = Color.White.copy(alpha = 0.8f),
                            unfocusedContainerColor = Color.White.copy(alpha = 0.8f)
                        ),
                        keyboardActions = KeyboardActions(onDone = {
                            // 处理URL输入
                            val webView = viewModel.getWebView() ?: activity.tokenInfoWebView
                            when (viewModel.processUrlInput(urlInputText, webView, token)) {
                                is UrlInputState.ValidUrl, is UrlInputState.ValidEventId -> {
                                    isUrlInputVisible = false
                                    focusManager.clearFocus()
                                }
                                is UrlInputState.InvalidInput -> {
                                    Toast.makeText(context, "Invalid URL", Toast.LENGTH_SHORT).show()
                                }
                                else -> {} // Idle状态不处理
                            }
                        })
                    )
                }
            }
        }
    )
} 