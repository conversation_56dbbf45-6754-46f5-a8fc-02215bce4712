package dev.pigmomo.yhhelper2025.ui.viewmodel

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.ClipboardManager
import dev.pigmomo.yhhelper2025.utils.ClipboardUtils
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.pigmomo.yhhelper2025.data.model.TokenEntity
import dev.pigmomo.yhhelper2025.data.repository.TokenRepository
import dev.pigmomo.yhhelper2025.data.model.LinkData
import dev.pigmomo.yhhelper2025.utils.OkHttpUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import android.util.Log
import org.json.JSONArray

/**
 * MainViewModel - 管理主界面的状态和业务逻辑
 */
class MainViewModel(val tokenRepository: TokenRepository) : ViewModel() {
    // 令牌列表状态
    private val _tokens = MutableStateFlow<List<TokenEntity>>(emptyList())
    val tokens: StateFlow<List<TokenEntity>> = _tokens.asStateFlow()

    // UI状态
    var isSearchVisible by mutableStateOf(false)
    var currentClickIndex by mutableIntStateOf(-1)
    var isActionDialogVisible by mutableStateOf(false)
    var isNoteDialogVisible by mutableStateOf(false)
    var isDeleteDialogVisible by mutableStateOf(false)
    var isResetDialogVisible by mutableStateOf(false)
    var bottomAppBarMenuExpanded by mutableStateOf(false)
    var insertTokenDialog by mutableStateOf(false)

    // 记录最近点击的链接内容，而不是索引
    var recentClickedKeyParam by mutableStateOf("")

    // 链接对话框状态
    var showLinksDialog by mutableStateOf(false)

    // 搜索相关状态
    var searchTargetIndex by mutableIntStateOf(0)
    var previousTargetIndex by mutableIntStateOf(-1)
    var searchTips by mutableStateOf("")
    var filteredTokens by mutableStateOf(emptyList<TokenEntity>())

    // 剪贴板相关状态
    var clipboardText by mutableStateOf("")
    var clipboardTextType by mutableStateOf("")
    var showClipboardText by mutableStateOf(false)
    var checkClipboardOnResume by mutableStateOf(false)

    // 链接数据状态
    private val _links = MutableStateFlow<List<LinkData>>(emptyList())
    val links: StateFlow<List<LinkData>> = _links.asStateFlow()

    // 网络请求状态
    var isLoading by mutableStateOf(false)
    var loadedData by mutableStateOf("")
    var loadError by mutableStateOf("")
    var lastUpdateTime by mutableStateOf("")
    var serverStatus by mutableStateOf(false)

    init {
        loadAllTokens()
        // 检查数据库连接是否正常
        viewModelScope.launch {
            try {
                val count = tokens.value.size
                Log.d("MainViewModel", "Database connection check: token count = $count")
            } catch (e: Exception) {
                Log.e("MainViewModel", "Database connection error: ${e.message}")
                try {
                    // 尝试重新打开数据库连接
                    tokenRepository.reopenDatabase()
                    // 重新加载数据
                    loadAllTokens()
                } catch (innerEx: Exception) {
                    Log.e("MainViewModel", "Failed to recover database connection: ${innerEx.message}")
                }
            }
        }
    }

    /**
     * 加载所有令牌数据
     */
    fun loadAllTokens() {
        viewModelScope.launch {
            tokenRepository.getAllTokens().collect { tokenList ->
                _tokens.value = tokenList
                if (!isSearchVisible) {
                    updateTokenStatistics()
                }
            }
        }
    }

    /**
     * 更新令牌统计信息
     */
    fun updateTokenStatistics() {
        val tokenList = _tokens.value
        if (tokenList.isEmpty()) {
            searchTips = ""
            return
        }

        val totalCount = tokenList.size
        val oldTokensCount = tokenList.count { !it.isNew && !it.activityLimited }
        val activityLimitedCount = tokenList.count { it.activityLimited }

        searchTips = "共${totalCount}个TOKEN，老号$oldTokensCount，黑号$activityLimitedCount"
    }

    /**
     * 更新令牌
     */
    fun updateToken(token: TokenEntity) {
        viewModelScope.launch {
            tokenRepository.updateToken(token)
            // 更新内存中的列表以立即刷新UI
            val updatedList = _tokens.value.toMutableList()
            val index = updatedList.indexOfFirst { it.uid == token.uid }
            if (index != -1) {
                updatedList[index] = token
                _tokens.value = updatedList
            }
            if (!isSearchVisible) {
                updateTokenStatistics()
            }
        }
    }

    /**
     * 删除令牌
     */
    fun deleteToken(token: TokenEntity) {
        viewModelScope.launch {
            tokenRepository.deleteTokenByUid(token.uid)
            // 更新内存中的列表以立即刷新UI
            val updatedList = _tokens.value.toMutableList()
            updatedList.removeIf { it.uid == token.uid }
            _tokens.value = updatedList
            if (!isSearchVisible) {
                updateTokenStatistics()
            }
        }
    }

    /**
     * 标记令牌为旧的
     */
    fun markTokenAsOld(index: Int) {
        if (index < 0 || index >= _tokens.value.size) return

        viewModelScope.launch {
            val token = _tokens.value[index]
            val updatedToken = token.copy(isNew = false)
            tokenRepository.updateToken(updatedToken)

            // 更新内存中的列表以立即刷新UI
            val updatedList = _tokens.value.toMutableList()
            updatedList[index] = updatedToken
            _tokens.value = updatedList
            if (!isSearchVisible) {
                updateTokenStatistics()
            }
        }
    }

    /**
     * 标记令牌为黑号
     */
    fun markTokenAsLimited(index: Int) {
        if (index < 0 || index >= _tokens.value.size) return

        viewModelScope.launch {
            val token = _tokens.value[index]
            val updatedToken = token.copy(
                isNew = false,
                activityLimited = true,
                yhCardLimited = true
            )
            tokenRepository.updateToken(updatedToken)

            // 更新内存中的列表以立即刷新UI
            val updatedList = _tokens.value.toMutableList()
            updatedList[index] = updatedToken
            _tokens.value = updatedList
            if (!isSearchVisible) {
                updateTokenStatistics()
            }
        }
    }

    /**
     * 重置令牌状态
     */
    fun resetToken(index: Int) {
        if (index < 0 || index >= _tokens.value.size) return

        viewModelScope.launch {
            val token = _tokens.value[index]
            val updatedToken = token.copy(
                isNew = true,
                bargainFirst = true,
                activityLimited = false,
                yhCardLimited = false,
                helpCouponCount = "{\"0000\":[\"1970-01-01\",0,[\"0000\"]]}",
                pointExchangeCount = "[\"1970-01-01\",0,[\"0000\"]]",
                spellLuckCount = "[\"1970-01-01\",0,[\"0000\"]]"
            )
            tokenRepository.updateToken(updatedToken)

            // 更新内存中的列表以立即刷新UI
            val updatedList = _tokens.value.toMutableList()
            updatedList[index] = updatedToken
            _tokens.value = updatedList
            if (!isSearchVisible) {
                updateTokenStatistics()
            }
        }
    }

    /**
     * 更新令牌备注
     */
    fun updateTokenNote(index: Int, newNote: String) {
        if (index < 0 || index >= _tokens.value.size) return

        viewModelScope.launch {
            val token = _tokens.value[index]
            val updatedToken = token.copy(extraNote = newNote)
            tokenRepository.updateToken(updatedToken)

            // 更新内存中的列表以立即刷新UI
            val updatedList = _tokens.value.toMutableList()
            updatedList[index] = updatedToken
            _tokens.value = updatedList
        }
    }

    /**
     * 复制令牌信息到剪贴板
     */
    fun copyToken(clipboardManager: ClipboardManager, token: TokenEntity) {
        ClipboardUtils.copyToken(clipboardManager, token)
    }

    /**
     * 检查剪贴板文本类型
     */
    fun checkClipboardTextType(text: String): String {
        return ClipboardUtils.checkClipboardTextType(text)
    }

    /**
     * 处理剪贴板文本变化
     */
    fun handleClipboardTextChange(clipboardManager: ClipboardManager) {
        val text = clipboardManager.getText()
        if (text != null && text.toString() != clipboardText) {
            val textType = ClipboardUtils.checkClipboardTextType(text.toString())
            if (textType.isNotEmpty()) {
                clipboardText = text.toString()
                clipboardTextType = textType
                showClipboardText = true
            }
        }
    }

    /**
     * 清空搜索结果
     */
    fun clearSearchResults() {
        filteredTokens = emptyList()
        previousTargetIndex = -1
        updateTokenStatistics()
    }

    /**
     * 从本地HTTP服务获取链接数据，并创建带有使用状态的副本
     */
    fun fetchLinksFromLocalServer() {
        viewModelScope.launch {
            isLoading = true
            loadError = ""

            try {
                // 先检查服务是否可用
                serverStatus = OkHttpUtils.isLocalServerRunning()
                if (!serverStatus) {
                    loadError = "本地服务未运行"
                    isLoading = false
                    return@launch
                }

                // 获取数据
                val jsonData = OkHttpUtils.fetchDataFromLocalServer()
                if (jsonData.isNotEmpty()) {
                    loadedData = jsonData
                    lastUpdateTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                        .format(Date())

                    // 解析JSON数据
                    try {
                        val gson = Gson()
                        val linkListType = object : TypeToken<List<LinkData>>() {}.type
                        val linkDataList: List<LinkData> = gson.fromJson(jsonData, linkListType)

                        _links.value = linkDataList.reversed()
                    } catch (e: Exception) {
                        loadError = "解析数据失败: ${e.message}"
                    }
                } else {
                    loadError = "获取数据为空"
                }
            } catch (e: Exception) {
                loadError = "获取数据失败: ${e.message}"
            } finally {
                isLoading = false
            }
        }
    }

    /**
     * 保存链接使用状态到服务器
     */
    fun saveLinksUsageStatus() {
        viewModelScope.launch {
            if (!serverStatus || _links.value.isEmpty()) {
                return@launch
            }

            try {
                // 构建使用状态JSON数据
                val usedLinks = _links.value.filter { it.isUsed }
                    .map { it.keyParam }

                val unusedLinks = _links.value.filter { !it.isUsed }
                    .map { it.keyParam }

                val jsonObject = JSONObject().apply {
                    put("usedKeyParam", JSONArray(usedLinks))
                    put("unusedKeyParam", JSONArray(unusedLinks))
                    put("updateTime", System.currentTimeMillis())
                }

                // 发送数据到服务器
                OkHttpUtils.postJsonData("updateUsage", jsonObject.toString())

            } catch (e: Exception) {
                Log.e("MainViewModel", "保存链接使用状态失败: ${e.message}")
            }
        }
    }

    /**
     * 更新链接的使用状态
     * @param index 链接在列表中的索引
     * @param isUsed 是否已使用
     */
    fun updateLinkUsageStatus(index: Int, isUsed: Boolean) {
        if (index < 0 || index >= _links.value.size) return

        val updatedList = _links.value.toMutableList()
        updatedList[index] = updatedList[index].copy(isUsed = isUsed)
        _links.value = updatedList
    }
}