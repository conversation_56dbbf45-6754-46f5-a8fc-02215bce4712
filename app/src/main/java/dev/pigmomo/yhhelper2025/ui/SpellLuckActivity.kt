package dev.pigmomo.yhhelper2025.ui

import android.annotation.SuppressLint
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Bitmap
import android.os.Bundle
import android.view.ViewGroup
import android.webkit.CookieManager
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.outlined.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.ViewModelProvider
import dev.pigmomo.yhhelper2025.R
import dev.pigmomo.yhhelper2025.data.model.ShopInfo
import dev.pigmomo.yhhelper2025.data.model.TokenEntity
import dev.pigmomo.yhhelper2025.ui.theme.Yhhelper2025Theme
import dev.pigmomo.yhhelper2025.ui.viewmodel.PointExchangeViewModel
import dev.pigmomo.yhhelper2025.ui.viewmodel.SpellLuckViewModel
import dev.pigmomo.yhhelper2025.ui.viewmodel.ViewModelFactory
import dev.pigmomo.yhhelper2025.utils.WebUtils
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.regex.Pattern

/**
 * 拼手气活动界面
 */
class SpellLuckActivity : ComponentActivity() {
    // ViewModel实例
    private lateinit var viewModel: SpellLuckViewModel
    // 从Intent传递的keyParam参数
    private var keyParam: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 获取Intent中的keyParam参数
        keyParam = intent.getStringExtra("keyParam")

        setContent {
            Yhhelper2025Theme(dynamicColor = false) {
                viewModel = ViewModelProvider(
                    this,
                    ViewModelFactory.getInstance(this)
                )[SpellLuckViewModel::class.java]

                SpellLuckScreen(viewModel, onBack = { finish() }, keyParam)
            }
        }
    }
}

/**
 * 拼手气主界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@SuppressLint("SetJavaScriptEnabled")
@Composable
fun SpellLuckScreen(
    viewModel: SpellLuckViewModel,
    onBack: () -> Unit,
    keyParam: String? = null
) {
    val context = LocalContext.current
    val clipboardManager = LocalClipboardManager.current
    val focusManager = LocalFocusManager.current
    val coroutineScope = rememberCoroutineScope()

    val tokens by viewModel.tokens.collectAsState()

    // WebView实例
    var spellLuckWebView: WebView by remember { mutableStateOf(WebView(context)) }

    // 检查keyParam或剪贴板中是否有eventId
    LaunchedEffect(Unit) {
        // 如果有keyParam参数，则直接使用，不从剪贴板获取
        if (!keyParam.isNullOrEmpty()) {
            if (viewModel.processKeyParam(keyParam)) {
                return@LaunchedEffect
            }
        }
        
        // 只有当没有keyParam参数或解析失败时，才尝试从剪贴板获取
        viewModel.processClipboardContent(clipboardManager)
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Row{
                        Text("拼手气", maxLines = 1)
                        Text(
                            viewModel.spellLuckTips,
                            fontSize = 10.sp,
                            maxLines = 1,
                            modifier = Modifier
                                .padding(start = 5.dp)
                                .offset(y= (5).dp)
                        )
                    }
                },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(
                            Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    actionIconContentColor = Color.White
                )
            )
        },
        bottomBar = {
            BottomAppBar(
                actions = {
                    OutlinedTextField(
                        value = viewModel.inputEventId,
                        onValueChange = { newValue ->
                            if (newValue.length <= 19 && newValue.all { it.isDigit() }) {
                                viewModel.inputEventId = newValue
                            }
                        },
                        label = { Text("EventId") },
                        modifier = Modifier
                            .offset { IntOffset(x = 45, y = -15) },
                        keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Number),
                        shape = RoundedCornerShape(24),
                        singleLine = true
                    )
                },
                floatingActionButton = {
                    FloatingActionButton(
                        onClick = {
                            if (viewModel.inputEventId.isNotEmpty() && viewModel.inputEventId.length >= 16) {
                                if (!viewModel.isStarted) {
                                    focusManager.clearFocus()
                                    coroutineScope.launch {
                                        viewModel.getTokensToSpellLuck()

                                        // 使用collectLatest监听tokens的变化，确保获取到最新结果
                                        viewModel.tokens.collectLatest { latestTokens ->
                                            // 只有当tokens已经被更新时才执行后续操作
                                            if (latestTokens.isEmpty()) {
                                                // 如果tokens为空，不执行任何操作
                                                return@collectLatest
                                            }

                                            viewModel.isStarted = true

                                            // 处理积分组队URL
                                            val loadUrl = viewModel.dealWithSpellLuckUrl(
                                                latestTokens[0],
                                                viewModel.inputEventId
                                            )
                                            withContext(Dispatchers.Main) {
                                                spellLuckWebView.settings.userAgentString =
                                                    WebUtils.generateUserAgent(latestTokens[0].appParam)
                                                spellLuckWebView.loadUrl(loadUrl)
                                            }
                                            // 一旦执行完操作，取消收集
                                            this.cancel()
                                        }
                                    }

                                } else {
                                    viewModel.showOperationDialog = true
                                }
                            } else {
                                viewModel.spellLuckTips = "请输入正确的EventId"
                            }
                        },
                        containerColor = BottomAppBarDefaults.bottomAppBarFabColor,
                        elevation = FloatingActionButtonDefaults.bottomAppBarFabElevation(),
                    ) {
                        Icon(
                            if (!viewModel.isStarted) ImageVector.vectorResource(R.drawable.ic_start) else Icons.Outlined.Check,
                            contentDescription = null,
                            modifier = Modifier
                                .size(width = 24.dp, height = 24.dp)
                                .let {
                                    if (viewModel.isStarted) it else it.offset {
                                        IntOffset(
                                            x = 5,
                                            y = 0
                                        )
                                    }
                                }
                        )
                    }
                }
            )
        }
    ) { innerPadding ->
        Box(modifier = Modifier.padding(innerPadding)) {
            // 加载进度条
            LinearProgressIndicator(
                progress = { viewModel.urlLoadProgress / 100f },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(3.dp),
            )

            // WebView
            AndroidView(
                factory = {
                    WebView(context).apply {
                        spellLuckWebView = this
                        layoutParams = ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.MATCH_PARENT
                        )

                        // 配置WebView
                        settings.apply {
                            javaScriptEnabled = true
                            userAgentString = WebUtils.generateUserAgent("")
                            domStorageEnabled = true
                            databaseEnabled = true
                            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                        }

                        // 设置WebChromeClient以监控加载进度
                        webChromeClient = object : WebChromeClient() {
                            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                                viewModel.urlLoadProgress = newProgress
                            }
                        }

                        // 初始加载URL
                        viewModel.loadInitialUrl(this)
                    }
                },
                modifier = Modifier.fillMaxSize()
            )

            // 操作对话框
            if (viewModel.showOperationDialog) {
                SpellLuckOperationDialog(
                    viewModel = viewModel,
                    tokens = tokens,
                    clipboardManager = clipboardManager,
                    spellLuckWebView = spellLuckWebView,
                    coroutineScope = coroutineScope
                )
            }
        }
    }
}

/**
 * 拼手气操作对话框
 */
@Composable
private fun SpellLuckOperationDialog(
    viewModel: SpellLuckViewModel,
    tokens: List<TokenEntity>,
    clipboardManager: androidx.compose.ui.platform.ClipboardManager,
    spellLuckWebView: WebView,
    coroutineScope: kotlinx.coroutines.CoroutineScope
) {
    AlertDialog(
        onDismissRequest = { },
        modifier = Modifier.wrapContentSize(),
        title = { Text("拼手气通知") },
        text = {
            Row {
                Text("是否继续拼手气！")
            }
        },
        containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.8f),
        confirmButton = {
            Column {
                Button(
                    onClick = {
                        // 复制手机号到剪贴板
                        clipboardManager.setText(
                            androidx.compose.ui.text.AnnotatedString(tokens[0].phoneNumber)
                        )

                        // 标记Token状态并处理下一个Token
                        val currentToken = tokens[0]
                        //markTokenAsUsed(currentToken)

                        coroutineScope.launch {
                            // 更新Token并加载下一个Token
                            handleNextToken(
                                currentToken = currentToken,
                                tokens = tokens,
                                viewModel = viewModel,
                                spellLuckWebView = spellLuckWebView,
                                loadNextUrl = true,
                                coroutineScope = coroutineScope
                            )
                        }
                    }
                ) {
                    Text("跳过")
                }
                Button(
                    onClick = {
                        // 获取当前Token
                        val currentToken = tokens[0]

                        // 更新拼手气计数
                        viewModel.updateSpellLuckCount(
                            currentToken,
                            viewModel.inputEventId
                        )

                        coroutineScope.launch {
                            // 更新Token并加载下一个Token
                            handleNextToken(
                                currentToken = currentToken,
                                tokens = tokens,
                                viewModel = viewModel,
                                spellLuckWebView = spellLuckWebView,
                                loadNextUrl = true,
                                coroutineScope = coroutineScope
                            )
                        }
                    }
                ) {
                    Text("下一个")
                }
            }
        },
        dismissButton = {
            Column {
                Button(
                    onClick = {
                        endSpellLuckSession(viewModel)
                    }
                ) {
                    Text("结束/UN")
                }
                Button(
                    onClick = {
                        val currentToken = tokens[0]

                        viewModel.updateSpellLuckCount(
                            currentToken,
                            viewModel.inputEventId
                        )

                        coroutineScope.launch {
                            viewModel.updateToken(currentToken)
                            endSpellLuckSession(viewModel)
                        }
                    }
                ) {
                    Text("结束/SU")
                }
            }
        }
    )
}

/**
 * 处理下一个Token
 *
 * @param currentToken 当前Token
 * @param tokens Token列表
 * @param viewModel ViewModel实例
 * @param spellLuckWebView WebView实例
 * @param loadNextUrl 是否加载下一个URL
 * @param coroutineScope 协程作用域
 */
private suspend fun handleNextToken(
    currentToken: TokenEntity,
    tokens: List<TokenEntity>,
    viewModel: SpellLuckViewModel,
    spellLuckWebView: WebView,
    loadNextUrl: Boolean,
    coroutineScope: kotlinx.coroutines.CoroutineScope
) {
    // 更新Token
    viewModel.updateToken(currentToken)

    // 获取更新后的Token列表
    val updatedTokens = viewModel.tokens.value

    if (updatedTokens.isEmpty()) {
        viewModel.spellLuckTips = "该 EventId 无可用 TOKEN"
        viewModel.isStarted = false
        viewModel.showOperationDialog = false
    } else if (loadNextUrl) {
        viewModel.spellLuckTips = "可用 ${updatedTokens.size} 个TOKEN"
        val loadUrl = viewModel.dealWithSpellLuckUrl(
            updatedTokens[0],
            viewModel.inputEventId
        )
        withContext(Dispatchers.Main) {
            spellLuckWebView.settings.userAgentString =
                WebUtils.generateUserAgent(updatedTokens[0].appParam)
            spellLuckWebView.loadUrl(loadUrl)
            viewModel.showOperationDialog = false
        }
    }
}

/**
 * 结束积分组队会话
 * @param viewModel ViewModel实例
 */
private fun endSpellLuckSession(
    viewModel: SpellLuckViewModel
) {
    viewModel.spellLuckTips = "助力结束"
    viewModel.isStarted = false
    viewModel.showOperationDialog = false
}

