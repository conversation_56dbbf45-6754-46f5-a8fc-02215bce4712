package dev.pigmomo.yhhelper2025.ui.components

import android.content.Intent
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhhelper2025.data.model.TokenEntity
import dev.pigmomo.yhhelper2025.ui.TokenInfoActivity

/**
 * 令牌项组件，显示令牌信息的卡片
 *
 * @param token 要显示的令牌实体
 * @param index 当前令牌在列表中的索引
 * @param totalCount 令牌列表的总数量
 * @param isSelected 是否被选中
 * @param isHighlighted 是否被高亮显示（搜索结果）
 * @param onLongClick 长按回调函数
 * @param itemHeight 卡片高度
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun TokenItem(
    token: TokenEntity,
    index: Int,
    totalCount: Int,
    isSelected: Boolean = false,
    isHighlighted: Boolean = false,
    onClick: () -> Unit = {},
    onLongClick: () -> Unit = {},
    itemHeight: Int = 86
) {
    val context = LocalContext.current
    val tokenStatus = when {
        token.yhCardLimited -> "黑"
        token.activityLimited -> "黑"
        else -> if (token.isNew) "新" else "老"
    }
    val note = if (token.extraNote.isEmpty()) "" else "Note: " + token.extraNote
    Card(
        shape = RoundedCornerShape(8.dp),
        modifier = Modifier
            .padding(horizontal = 8.dp, vertical = 0.dp)
            .height(itemHeight.dp - 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = when {
                isHighlighted -> Color(0xFFEF5350)
                isSelected -> Color(0xFFFFCDD2)
                else -> Color(0xFFFDEDF0)
            },
        )
    ) {
        Box(
            contentAlignment = Alignment.CenterStart,
            modifier = Modifier
                .fillMaxWidth()
                .height(78.dp)
                .combinedClickable(
                    onClick = onClick,
                    onLongClick = onLongClick
                )
                .padding(start = 16.dp, end = 6.dp, top = 6.dp, bottom = 6.dp),
        ) {
            Column {
                Text(
                    text = "${token.phoneNumber},${token.uid}",
                    style = MaterialTheme.typography.bodyLarge,
                    maxLines = 1
                )
                Text(
                    text = "Info: $tokenStatus Date: ${token.updateDate} $note",
                    style = MaterialTheme.typography.bodyMedium,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1
                )
            }
            Text(
                text = "${index + 1}/$totalCount",
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(end = 0.dp, bottom = 0.dp),
                maxLines = 1,
                color = Color.Gray,
                fontSize = 8.sp
            )
        }
    }
}