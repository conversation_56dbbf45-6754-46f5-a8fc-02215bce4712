package dev.pigmomo.yhhelper2025.ui.viewmodel

import android.webkit.WebView
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.ClipboardManager
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.pigmomo.yhhelper2025.data.model.ShopDataProvider
import dev.pigmomo.yhhelper2025.data.model.ShopInfo
import dev.pigmomo.yhhelper2025.data.model.TokenEntity
import dev.pigmomo.yhhelper2025.data.repository.TokenRepository
import dev.pigmomo.yhhelper2025.utils.UrlParamUtils
import dev.pigmomo.yhhelper2025.utils.WebUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.UUID
import kotlin.random.Random

/**
 * HelpCouponViewModel - 管理助力券界面的状态和业务逻辑
 */
class HelpCouponViewModel(private val tokenRepository: TokenRepository) : ViewModel() {
    // 状态管理
    var helpCouponTips by mutableStateOf("")
    var settingExpanded by mutableStateOf(false)
    var needNewToken by mutableStateOf(false)
    var showOperationDialog by mutableStateOf(false)
    
    // 输入字段
    var inputPrizeId by mutableStateOf("")
    var inputGameCode by mutableStateOf("")
    
    // 商店信息
    private val _shopInfoList = MutableStateFlow<List<ShopInfo>>(ShopDataProvider.getShopInfoList())
    val shopInfoList: StateFlow<List<ShopInfo>> = _shopInfoList.asStateFlow()
    
    var selectedShopInfo by mutableStateOf<ShopInfo?>(null)
    
    // 可用Token列表
    private val _tokens = MutableStateFlow<List<TokenEntity>>(emptyList())
    val tokens: StateFlow<List<TokenEntity>> = _tokens.asStateFlow()
    
    // WebView加载进度
    var urlLoadProgress by mutableStateOf(0)
    
    init {
        // 初始化选中的商店信息
        viewModelScope.launch {
            _shopInfoList.value.firstOrNull()?.let {
                selectedShopInfo = it
            }
        }
    }
    
    /**
     * 从参数中提取GameCode和PrizeId
     * 
     * @param param 可能包含GameCode和PrizeId的参数
     * @return 是否成功设置了至少一个参数
     */
    fun processKeyParam(param: String?): Boolean {
        var paramProcessed = false
        
        // 提取GameCode
        val gameCode = UrlParamUtils.extractGameCode(param)
        if (gameCode != null) {
            inputGameCode = gameCode
            paramProcessed = true
        }
        
        // 提取PrizeId
        val prizeId = UrlParamUtils.extractPrizeId(param)
        if (prizeId != null) {
            inputPrizeId = prizeId
            paramProcessed = true
        }
        
        return paramProcessed
    }
    
    /**
     * 从剪贴板提取GameCode和PrizeId
     * 
     * @param clipboardManager 剪贴板管理器
     * @return 是否成功设置了至少一个参数
     */
    fun processClipboardContent(clipboardManager: ClipboardManager): Boolean {
        val clipboardContent = clipboardManager.getText()?.toString() ?: return false
        var paramProcessed = false
        
        // 提取GameCode
        val gameCode = UrlParamUtils.extractGameCode(clipboardContent)
        if (gameCode != null) {
            inputGameCode = gameCode
            paramProcessed = true
        }
        
        // 提取PrizeId
        val prizeId = UrlParamUtils.extractPrizeId(clipboardContent)
        if (prizeId != null) {
            inputPrizeId = prizeId
            paramProcessed = true
        }
        
        return paramProcessed
    }
    
    /**
     * 获取可用于助力的Token列表
     */
    fun getTokensToHelpCoupon() {
        if (inputPrizeId.isEmpty()) {
            helpCouponTips = "请输入PrizeID"
            return
        }
        
        viewModelScope.launch {
            val tokenList = tokenRepository.getTokensToHelpCoupon(inputPrizeId, needNewToken)
            _tokens.value = tokenList
            
            if (tokenList.isEmpty()) {
                helpCouponTips = "该 PRIZEID 无可用 TOKEN"
            } else {
                helpCouponTips = "可用 ${tokenList.size} 个TOKEN"
            }
        }
    }
    
    /**
     * 处理助力券URL
     */
    fun dealWithHelpCouponUrl(
        token: TokenEntity,
        switchedShopInfo: ShopInfo,
        inputPrizeId: String,
        inputGameCode: String
    ): String {
        val appParamArr = token.appParam.split(",")
        return "https://m.yonghuivip.com/yh-m-site/yh-helpcoupon/index.html?platform=android&mid=ShareCoupon2.0&sid=wxhy&cid=Default&needlocation=1&canShare=1&prizeId=$inputPrizeId&gameCode=$inputGameCode&isShare=1&needlogin=1&isForward=0&unionId=&opId=&cityid=${switchedShopInfo.cityId}&lng=${switchedShopInfo.lng}&lat=${switchedShopInfo.lat}&userlng=&userlat=&addressId=&appid=wxc9cf7c95499ee604&v=${appParamArr[8]}&fromorigin=miniprogram&mobile=&deviceid=" + appParamArr[2] + "&userid=${token.uid}&sellerid=${switchedShopInfo.sellerId}&shopid=${switchedShopInfo.shopId}&sellername=&shopname=${switchedShopInfo.shopName}&scDistinctId=&FPName=https%3A%2F%2Fm.yonghuivip.com%2Fyh-m-site%2Fyh-helpcoupon%2Findex.html%3Fplatform%3Dwechatminiprogram%26mid%3DShareCoupon2.0%26sid%3Dpyq%26cid%3DDefault%26needlocation%3D1%26canShare%3D1%26prizeId%3D$inputPrizeId%26gameCode%3D$inputGameCode%26isShare%3D1%26needlogin%3D1&PPName=&sceId=&project_name=yh_life&pub_v=19&yh_appName=&trace_id=&span_id=&showmultiseller=&trackPub=%7B%22yh_openGId%22%3A%22-99%22%2C%22yh_isLocationAllowed%22%3A%221%22%2C%22yh_sceneId%22%3A%221036%22%2C%22yh_sceneName%22%3A%22App%20%E5%88%86%E4%BA%AB%E6%B6%88%E6%81%AF%E5%8D%A1%E7%89%87%22%2C%22yh_abVersion%22%3A%22-99%22%2C%22yh_chanShopId%22%3A%22-99%22%2C%22yh_roomId%22%3A%22-99%22%2C%22yh_weixinadinfo%22%3A%22-99%22%2C%22yh_chanId%22%3A%22-99%22%2C%22yh_userType%22%3A%22-99%22%2C%22yh_sharedRelationships%22%3A%22-99%22%7D&type=1&token=${token.userKey + "-601933-" + token.accessToken}" + "&brand=${appParamArr[7]}&model=${appParamArr[5]}&os=android&osVersion=${appParamArr[4]}&screen=${appParamArr[1]}" + "&productLine=YhStore&channelSub=&appType=mini&proportion=2.75&networkType=${appParamArr[6]}&channel=512&jysessionid=" + WebUtils.generateRandomJySessionId()
    }
    
    /**
     * 加载初始URL
     */
    fun loadInitialUrl(webView: WebView) {
        selectedShopInfo?.let { shopInfo ->
            val url = "https://m.yonghuivip.com/yh-m-site/yh-helpcoupon/index.html?nfcolor=FFFFFF&ngbcolor=FF2742&needlogin=1&needlocation=1&isForward=0&unionId=&opId=&cityid=${shopInfo.cityId}&lng=${shopInfo.lng}&lat=${shopInfo.lat}&userlng=&userlat=&addressId=&appid=wxc9cf7c95499ee604&v=*********&fromorigin=miniprogram&mobile=&deviceid=8bdf15ee-f1f8-43e4-913c-fafc2dcb813a&platform=android&userid=694319091981073469&sellerid=${shopInfo.sellerId}&shopid=${shopInfo.shopId}&sellername=%E6%B0%B8%E8%BE%89%E8%B6%85%E5%B8%82&shopname=%E9%87%8D%E5%BA%86%E4%BB%81%E6%82%A6%E5%A4%A9%E5%9C%B0%E5%BA%97&scDistinctId=694319091981073469&FPName=%E6%88%91%E7%9A%84%E9%A1%B5&PPName=%E6%88%91%E7%9A%84%E9%A1%B5&sceId=1089&project_name=yh_life&pub_v=19&yh_appName=%E6%B0%B8%E8%BE%89%E7%94%9F%E6%B4%BB&trace_id=&span_id=&mid=jfsc&sid=VIP&cid=zdgf&showmultiseller=&trackPub=%7B%22yh_openGId%22%3A%22-99%22%2C%22yh_isLocationAllowed%22%3A%221%22%2C%22yh_sceneId%22%3A%221089%22%2C%22yh_sceneName%22%3A%22%E5%BE%AE%E4%BF%A1%E8%81%8A%E5%A4%A9%E4%B8%BB%E7%95%8C%E9%9D%A2%E4%B8%8B%E6%8B%89%22%2C%22yh_abVersion%22%3A%22-99%22%2C%22yh_chanShopId%22%3A%22-99%22%2C%22yh_roomId%22%3A%22-99%22%2C%22yh_weixinadinfo%22%3A%22-99%22%2C%22yh_chanId%22%3A%22-99%22%2C%22yh_userType%22%3A%22-99%22%2C%22yh_sharedRelationships%22%3A%22-99%22%7D&token=34ec3fa07353904dd02672fcb31b3be38744f47410e05ec89a61a0eeec0c9a4a121fa3d56656f70b1110bedd44df7a7e46bc24d42bd023f91a31bd6cb0bcd81a661fc8c76dc281fa2f9066dc6b59c11c-601933-34ec3fa07353904dd02672fcb31b3be38744f47410e05ec89a61a0eeec0c9a4a121fa3d56656f70b1110bedd44df7a7e46bc24d42bd023f91a31bd6cb0bcd81a661fc8c76dc281fa2f9066dc6b59c11c&brand=redmi&model=23013RK75C&os=android&osVersion=Android%2013&screen=1442*3202.5&productLine=YhStore&channelSub=&appType=mini&proportion=3.5&networkType=wifi&channel=512&jysessionid=${WebUtils.generateRandomJySessionId()}"
            webView.loadUrl(url)
        }
    }
    
    /**
     * 更新Token信息
     * 
     * @param token 要更新的Token实体
     */
    suspend fun updateToken(token: TokenEntity) {
        // 更新Token到数据库
        tokenRepository.updateToken(token)
        
        // 直接从列表中移除已使用的Token
        _tokens.value = _tokens.value.filter { it.uid != token.uid }
    }
}