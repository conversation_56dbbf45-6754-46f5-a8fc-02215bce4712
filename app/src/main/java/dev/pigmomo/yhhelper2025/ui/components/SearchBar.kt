package dev.pigmomo.yhhelper2025.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.BottomAppBarDefaults
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import dev.pigmomo.yhhelper2025.data.model.TokenEntity

/**
 * 搜索栏组件
 *
 * @param visible 是否显示搜索栏
 * @param tokens 令牌列表
 * @param onSearchResult 搜索结果回调，参数为(过滤后的令牌列表, 目标索引, 提示信息)
 * @param topPadding 顶部内边距
 */
@Composable
fun SearchBar(
    visible: Boolean,
    tokens: List<TokenEntity>,
    onSearchResult: (List<TokenEntity>, Int, String) -> Unit,
    topPadding: Float
) {
    val focusRequester = remember { FocusRequester() }
    var searchKeyWord by remember { mutableStateOf("") }
    var filteredTokens by remember { mutableStateOf(emptyList<TokenEntity>()) }
    var filteredClickCount by remember { mutableIntStateOf(0) }

    // 当SearchBar不可见时，清空搜索关键词
    LaunchedEffect(visible) {
        if (!visible) {
            searchKeyWord = ""
            filteredTokens = emptyList()
            onSearchResult(emptyList(), -1, "")
        }
    }

    // 处理搜索结果并通知回调
    fun processSearchResults() {
        when (filteredTokens.size) {
            0 -> onSearchResult(emptyList(), -1, "搜索结果为空！")
            1 -> {
                val targetIndex = tokens.indexOf(filteredTokens[0])
                onSearchResult(filteredTokens, targetIndex, "只有1个搜索结果！已跳转！")
            }

            else -> {
                val targetIndex = if (filteredClickCount >= 0) {
                    val currentFilteredItem = filteredTokens[filteredClickCount]
                    tokens.indexOf(currentFilteredItem)
                } else {
                    -1
                }
                onSearchResult(
                    filteredTokens,
                    targetIndex,
                    if (filteredClickCount >= 0) {
                        "搜索结果 ${filteredTokens.size} 个，当前第 ${filteredClickCount + 1} 个"
                    } else {
                        "搜索结果 ${filteredTokens.size} 个"
                    }
                )
            }
        }
    }

    // 监听tokens变化，如果有关键词则刷新搜索结果
    LaunchedEffect(tokens) {
        if (searchKeyWord.isNotEmpty()) {
            val oldFilteredTokens = filteredTokens
            filteredTokens = filterTokens(tokens, searchKeyWord)

            // 如果搜索结果变化了，重新计算当前选中项
            if (filteredTokens != oldFilteredTokens) {
                // 尝试保持当前选中的项目
                val currentItem =
                    if (oldFilteredTokens.isNotEmpty() && filteredClickCount < oldFilteredTokens.size) {
                        oldFilteredTokens[filteredClickCount]
                    } else null

                // 如果之前选中的项目仍然在新的过滤结果中，保持相同的选中索引
                if (currentItem != null && filteredTokens.contains(currentItem)) {
                    filteredClickCount = filteredTokens.indexOf(currentItem)
                    // 这里是保持当前选中项目的情况，但我们仍然需要调用processSearchResults()
                    // 因为MainActivity需要知道搜索结果，只是不需要滚动列表
                } else if (filteredClickCount >= filteredTokens.size && filteredTokens.isNotEmpty()) {
                    // 如果选中索引越界，不进行任何操作
                    filteredClickCount = -1
                }

                processSearchResults()
            }
        }
    }

    AnimatedVisibility(
        visible = visible,
        enter = slideInVertically(initialOffsetY = { -it }),
        exit = slideOutVertically(targetOffsetY = { -it })
    ) {
        LaunchedEffect(Unit) {
            focusRequester.requestFocus()
        }

        Row {
            OutlinedTextField(
                value = searchKeyWord,
                onValueChange = { newValue ->
                    searchKeyWord = newValue
                    filteredClickCount = 0

                    if (searchKeyWord.isEmpty()) {
                        filteredTokens = emptyList()
                        onSearchResult(emptyList(), -1, "请输入关键词！")
                        return@OutlinedTextField
                    }

                    filteredTokens = filterTokens(tokens, searchKeyWord)
                    processSearchResults()
                },
                label = { Text("SearchKeyWord") },
                singleLine = true,
                modifier = Modifier
                    .padding(
                        start = 20.dp,
                        end = 20.dp,
                        top = topPadding.dp,
                        bottom = 0.dp
                    )
                    .weight(4f)
                    .focusRequester(focusRequester),
                shape = RoundedCornerShape(24),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedContainerColor = Color.White.copy(alpha = 0.8f),
                    unfocusedContainerColor = Color.White.copy(alpha = 0.8f)
                )
            )

            FloatingActionButton(
                containerColor = if (filteredTokens.isNotEmpty() && filteredTokens.size > 1)
                    BottomAppBarDefaults.bottomAppBarFabColor else Color.White,
                onClick = {
                    if (filteredTokens.isNotEmpty() && filteredTokens.size > 1) {
                        filteredClickCount++
                        if (filteredClickCount >= filteredTokens.size) {
                            filteredClickCount = 0
                        }

                        // 使用抽取的通用方法处理结果
                        processSearchResults()
                    }
                },
                modifier = Modifier
                    .padding(top = topPadding.dp + 8.dp, end = 20.dp)
                    .weight(1f)
            ) {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowDown,
                    contentDescription = "Next Result",
                    modifier = Modifier
                        .size(24.dp)
                        .clip(RoundedCornerShape(50))
                )
            }
        }
    }
}

/**
 * 过滤令牌列表
 *
 * @param tokens 要过滤的令牌列表
 * @param searchKeyWord 搜索关键词
 * @return 过滤后的令牌列表
 */
fun filterTokens(tokens: List<TokenEntity>, searchKeyWord: String): List<TokenEntity> {
    val containsLimitedKeyword = searchKeyWord.contains("黑号") || searchKeyWord.contains("黑")

    return tokens.filter { token ->
        (containsLimitedKeyword && token.activityLimited) ||
                (token.phoneNumber.contains(searchKeyWord) ||
                        token.updateDate.contains(searchKeyWord) ||
                        token.extraNote.contains(searchKeyWord) ||
                        (token.updateDate + " " + token.extraNote).contains(searchKeyWord))
    }
}