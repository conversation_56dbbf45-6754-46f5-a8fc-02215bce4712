package dev.pigmomo.yhhelper2025.ui.components

import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.unit.dp
import androidx.room.Update
import dev.pigmomo.yhhelper2025.data.model.TokenEntity
import dev.pigmomo.yhhelper2025.data.repository.TokenRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.regex.Pattern

/**
 * 令牌插入/编辑对话框
 *
 * @param onDismiss 取消回调
 * @param coroutineScope 协程作用域
 * @param tokenRepository 令牌仓库
 * @param tokensUpdate 令牌更新回调
 * @param initialTokenText 初始令牌文本
 * @param onScrollToToken 滚动到指定令牌的回调
 */
@Composable
fun TokenInsertDialog(
    onDismiss: () -> Unit,
    coroutineScope: CoroutineScope,
    tokenRepository: TokenRepository,
    tokensUpdate: () -> Unit,
    initialTokenText: String = "",
    onScrollToToken: (TokenEntity) -> Unit = {}
) {
    val focusRequester = remember { FocusRequester() }
    var statusMessage by remember { mutableStateOf("INPUT TOKEN") }
    var tokenInputText by remember { mutableStateOf(initialTokenText) }

    // 令牌字段
    var phoneNumber by remember { mutableStateOf("") }
    var uid by remember { mutableStateOf("") }
    var userKey by remember { mutableStateOf("") }
    var accessToken by remember { mutableStateOf("") }
    var refreshToken by remember { mutableStateOf("") }
    var appParam by remember { mutableStateOf("") }
    var updateDate by remember { mutableStateOf("") }
    var extraNote by remember { mutableStateOf("") }

    // 自动获取焦点
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }
    
    // 处理初始文本解析
    LaunchedEffect(initialTokenText) {
        if (initialTokenText.isNotEmpty()) {
            parseTokenString(
                initialTokenText,
                onParseSuccess = { phone, id, key, access, refresh, param, date, note ->
                    phoneNumber = phone
                    uid = id
                    userKey = key
                    accessToken = access
                    refreshToken = refresh
                    appParam = param
                    updateDate = date
                    extraNote = note
                    statusMessage = "TOKEN 解析成功"
                },
                onParseError = { errorMsg ->
                    statusMessage = errorMsg
                }
            )
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MaterialTheme.colorScheme.surfaceContainer,
        modifier = Modifier.wrapContentSize(),
        title = { Text(statusMessage) },
        text = {
            OutlinedTextField(
                value = tokenInputText,
                onValueChange = {
                    tokenInputText = it
                    parseTokenString(
                        it,
                        onParseSuccess = { phone, id, key, access, refresh, param, date, note ->
                            phoneNumber = phone
                            uid = id
                            userKey = key
                            accessToken = access
                            refreshToken = refresh
                            appParam = param
                            updateDate = date
                            extraNote = note
                            statusMessage = "TOKEN 解析成功"
                        },
                        onParseError = { errorMsg ->
                            statusMessage = errorMsg
                        }
                    )
                },
                modifier = Modifier
                    .height(152.dp)
                    .focusRequester(focusRequester),
                label = { Text("TOKEN") },
            )
        },
        confirmButton = {
            Button(
                onClick = {
                    if (phoneNumber.isBlank() || uid.isBlank() || userKey.isBlank() || accessToken.isBlank()) {
                        statusMessage = "TOKEN 不完整"
                        return@Button
                    }

                    val token = TokenEntity(
                        phoneNumber = phoneNumber,
                        uid = uid,
                        userKey = userKey,
                        accessToken = accessToken,
                        refreshToken = refreshToken,
                        expiresIn = 0,
                        updateDate = updateDate,
                        isNew = true,
                        bargainFirst = true,
                        activityLimited = false,
                        yhCardLimited = false,
                        appParam = appParam,
                        extraNote = extraNote,
                        helpCouponCount = "{\"0000\":[\"1970-01-01\",0,[\"0000\"]]}",
                        pointExchangeCount = "[\"1970-01-01\",0,[\"0000\"]]",
                        spellLuckCount = "[\"1970-01-01\",0,[\"0000\"]]"
                    )

                    coroutineScope.launch {
                        val insertResult = tokenRepository.insertToken(token)
                        val isExist = insertResult.first
                        val isSuccess = insertResult.second

                        statusMessage = if (isExist) {
                            "TOKEN 更新成功"
                        } else {
                            "TOKEN 添加成功"
                        }

                        // 如果操作成功，直接使用当前token进行滚动并关闭对话框
                        if (isSuccess) {
                            // 刷新令牌列表
                            tokensUpdate()
                            // 直接使用当前token，无需再次查找
                            onScrollToToken(token)
                        }
                    }

                    // 成功处理后清空输入
                    tokenInputText = ""
                }
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 解析令牌字符串
 *
 * @param tokenString 要解析的令牌字符串
 * @param onParseSuccess 解析成功回调
 * @param onParseError 解析失败回调
 */
private fun parseTokenString(
    tokenString: String,
    onParseSuccess: (String, String, String, String, String, String, String, String) -> Unit,
    onParseError: (String) -> Unit
) {
    if (!tokenString.contains("----")) {
        return
    }

    try {
        val segments = tokenString.split("----")
        if (segments.size < 5) {
            onParseError("TOKEN 格式不正确")
            return
        }

        val phoneNumber = segments[0]
        val uid = segments[1]

        // 解析userKey和accessToken
        val keyTokenParts = segments[2].split("-601933-")
        if (keyTokenParts.size != 2) {
            onParseError("userKey/accessToken格式不正确")
            return
        }
        val userKey = keyTokenParts[0]
        val accessToken = keyTokenParts[1]

        val refreshToken = segments[3]
        //兼容旧版本appParam，后续可删除
        val appParam = if (segments[4].split(",").size == 8) {
            segments[4] + ",11.2.6.0"
        } else {
            segments[4]
        }

        // 解析日期和备注
        val updateDate: String
        var extraNote = ""

        if (segments.size == 6) {
            if (segments[5].contains(" ")) {
                val dateNoteParts = segments[5].split(" ", limit = 2)
                updateDate = dateNoteParts[0]
                extraNote = dateNoteParts[1]
            } else {
                val dateFormat = SimpleDateFormat("MM.dd.yy", Locale.getDefault())
                val datePattern = Pattern.compile("\\b\\d{2}\\.\\d{2}\\.\\d{2}\\b")
                val dateMatcher = datePattern.matcher(segments[5])

                updateDate = if (dateMatcher.find()) {
                    dateMatcher.group()
                } else {
                    dateFormat.format(System.currentTimeMillis())
                }
            }
        } else {
            val dateFormat = SimpleDateFormat("MM.dd.yy", Locale.getDefault())
            updateDate = dateFormat.format(System.currentTimeMillis())
        }

        onParseSuccess(
            phoneNumber,
            uid,
            userKey,
            accessToken,
            refreshToken,
            appParam,
            updateDate,
            extraNote
        )
    } catch (e: Exception) {
        onParseError("解析错误: ${e.message}")
    }
}