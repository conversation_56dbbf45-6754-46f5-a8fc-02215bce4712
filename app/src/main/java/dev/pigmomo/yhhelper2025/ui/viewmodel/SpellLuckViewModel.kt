package dev.pigmomo.yhhelper2025.ui.viewmodel

import android.webkit.WebView
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.ClipboardManager
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.pigmomo.yhhelper2025.data.model.ShopInfo
import dev.pigmomo.yhhelper2025.data.model.TokenEntity
import dev.pigmomo.yhhelper2025.data.repository.TokenRepository
import dev.pigmomo.yhhelper2025.utils.UrlParamUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.UUID
import kotlin.random.Random

/**
 * SpellLuckViewModel - 管理拼手气界面的状态和业务逻辑
 */
class SpellLuckViewModel(private val tokenRepository: TokenRepository) : ViewModel() {
    // 状态管理
    var spellLuckTips by mutableStateOf("")
    var showOperationDialog by mutableStateOf(false)
    var isStarted by mutableStateOf(false)
    
    // 输入字段
    var inputEventId by mutableStateOf("")
    
    // 可用Token列表
    private val _tokens = MutableStateFlow<List<TokenEntity>>(emptyList())
    val tokens: StateFlow<List<TokenEntity>> = _tokens.asStateFlow()
    
    // WebView加载进度
    var urlLoadProgress by mutableIntStateOf(0)
    
    /**
     * 从参数中提取EventId
     * 
     * @param param 可能包含EventId的参数
     * @return 是否成功设置了EventId
     */
    fun processKeyParam(param: String?): Boolean {
        val eventId = UrlParamUtils.extractEventId(param) ?: return false
        inputEventId = eventId
        return true
    }
    
    /**
     * 从剪贴板提取EventId
     * 
     * @param clipboardManager 剪贴板管理器
     * @return 是否成功设置了EventId
     */
    fun processClipboardContent(clipboardManager: ClipboardManager): Boolean {
        val clipboardContent = clipboardManager.getText()?.toString() ?: return false
        val eventId = UrlParamUtils.extractEventId(clipboardContent) ?: return false
        inputEventId = eventId
        return true
    }
    
    /**
     * 获取可用于拼手气的Token列表
     */
    fun getTokensToSpellLuck() {
        if (inputEventId.isEmpty() || inputEventId.length != 19) {
            spellLuckTips = "请输入正确的EventId"
            return
        }
        
        viewModelScope.launch {
            val tokenList = tokenRepository.getTokensToSpellLuck(inputEventId)
            _tokens.value = tokenList

            spellLuckTips = if (tokenList.isEmpty()) {
                isStarted = false
                "该 EventId 无可用 TOKEN"
            } else {
                "可用 ${tokenList.size} 个TOKEN"
            }
        }
    }
    
    /**
     * 生成随机的会话ID
     */
    fun generateRandomJySessionId(): String {
        val uuid = UUID.randomUUID().toString().replace("-", "")
        val random = Random.nextInt(100000, 999999)
        return "$uuid$random"
    }
    
    /**
     * 处理拼手气URL
     */
    fun dealWithSpellLuckUrl(
        token: TokenEntity,
        eventId: String
    ): String {
        val appParamArr = token.appParam.split(",")
        return "https://m.yonghuivip.com/yh-m-site/yh-spell-luck/index.html?needlogin=0&canShare=1&needlocation=1&showLoading=1&activityScene=hand_luck&eventId=$eventId&isForward=0&unionId=&opId=&cityid=3&lng=&lat=&userlng=&userlat=&addressId=&appid=wxc9cf7c95499ee604&v=${appParamArr[8]}&fromorigin=miniprogram&mobile=&deviceid=${appParamArr[2]}&platform=android&userid=${token.uid}&sellerid=7&shopid=9088&sellername=永辉超市&shopname=&scDistinctId=${token.uid}&FPName=-99&PPName=-99&sceId=1007&project_name=yh_life&pub_v=19&yh_appName=永辉生活&trace_id=&span_id&mid=WeChat&sid=Tencentfreetraffic&cid=Chat&showmultiseller&trackPub={\"yh_openGId\":\"-99\",\"yh_isLocationAllowed\":\"1\",\"yh_sceneId\":\"1007\",\"yh_sceneName\":\"单人聊天会话中的小程序消息卡片\",\"yh_abVersion\":\"-99\",\"yh_chanShopId\":\"-99\",\"yh_roomId\":\"-99\",\"yh_weixinadinfo\":\"-99\",\"yh_chanId\":\"-99\",\"yh_userType\":\"-99\",\"yh_sharedRelationships\":\"-99\"}&token=${token.userKey + "-601933-" + token.accessToken}&brand=${appParamArr[7]}&model=${appParamArr[5]}&os=android&osVersion=${appParamArr[4]}&screen=${appParamArr[1]}&productLine=YhStore&channelSub=&appType=mini&proportion=2.75&networkType=4g&channel=512&jysessionid=${generateRandomJySessionId()}"
    }
    
    /**
     * 加载初始URL
     */
    fun loadInitialUrl(webView: WebView) {
        val url =
            "https://m.yonghuivip.com/yh-m-site/yh-spell-luck/index.html?needlogin=0&canShare=1&needlocation=1&showLoading=1&activityScene=hand_luck&eventId=1747579140491386880&isForward=0&unionId=oCjU7wEniZNxUyJV2eTzJETwx1h0&opId=oP_Ic0Zm3vu6JbtcY-_wevQ3pzKQ&cityid=3&lng=&lat=&userlng=&userlat=&addressId=&appid=wxc9cf7c95499ee604&v=*********&fromorigin=miniprogram&mobile=&deviceid=84325237-791c-45f5-8495-4eda7954cb3d&platform=android&userid=492558375475025960&sellerid=7&shopid=9088&sellername=%E6%B0%B8%E8%BE%89%E8%B6%85%E5%B8%82&shopname=%E9%87%8D%E5%BA%86%E5%8D%97%E5%B2%B8%E5%8C%BA-%E5%9B%9B%E5%85%AC%E9%87%8C%E5%BA%97&scDistinctId=492558375475025960&FPName=https%3A%2F%2Fm.yonghuivip.com%2Fyh-m-site%2Fyh-spell-luck%2Findex.html%3Fneedlogin%3D0%26canShare%3D1%26needlocation%3D1%26showLoading%3D1%26activityScene%3Dhand_luck%26eventId%3D1747579140491386880&PPName=-99&sceId=1007&project_name=yh_life&pub_v=19&yh_appName=%E6%B0%B8%E8%BE%89%E7%94%9F%E6%B4%BB&trace_id=1705508507875%5EQ3eR1AYl&span_id=1705508508107004&mid=WeChat&sid=Tencentfreetraffic&cid=Chat&showmultiseller=%7B%227%22%3A%229088%22%7D&trackPub=%7B%22yh_openGId%22%3A%22-99%22%2C%22yh_isLocationAllowed%22%3A%221%22%2C%22yh_sceneId%22%3A%221007%22%2C%22yh_sceneName%22%3A%22%E5%8D%95%E4%BA%BA%E8%81%8A%E5%A4%A9%E4%BC%9A%E8%AF%9D%E4%B8%AD%E7%9A%84%E5%B0%8F%E7%A8%8B%E5%BA%8F%E6%B6%88%E6%81%AF%E5%8D%A1%E7%89%87%22%2C%22yh_abVersion%22%3A%22-99%22%2C%22yh_chanShopId%22%3A%22-99%22%2C%22yh_roomId%22%3A%22-99%22%2C%22yh_weixinadinfo%22%3A%22-99%22%2C%22yh_chanId%22%3A%22-99%22%2C%22yh_userType%22%3A%22-99%22%2C%22yh_sharedRelationships%22%3A%22-99%22%7D&token=ef224d77f0ff2ac73eff60e5eef0cd16c27b706b554bca34bb6d24b632f22e2081235fbc8d0aaffcc30cc449d311d1096c22b5fd0a056ed6984fe391b96d72d3661fc8c76dc281fa2f9066dc6b59c11c-601933-ef224d77f0ff2ac73eff60e5eef0cd16c27b706b554bca34bb6d24b632f22e2081235fbc8d0aaffcc30cc449d311d1096c22b5fd0a056ed6984fe391b96d72d3661fc8c76dc281fa2f9066dc6b59c11c&brand=Xiaomi&model=Redmi%20K20%20Pro&os=android&osVersion=Android%2012&screen=1080.75*2340.25&productLine=YhStore&channelSub=&appType=mini&proportion=2.75&networkType=wifi&channel=512&jysessionid=${generateRandomJySessionId()}"
        webView.loadUrl(url)
    }
    
    /**
     * 更新Token信息
     * 
     * @param token 要更新的Token实体
     */
    suspend fun updateToken(token: TokenEntity) {
        // 更新Token到数据库
        tokenRepository.updateToken(token)

        // 直接从列表中移除已使用的Token
        _tokens.value = _tokens.value.filter { it.uid != token.uid }
    }
    
    /**
     * 更新拼手气计数
     * 
     * @param token 要更新的Token
     * @param eventId 事件ID
     */
    fun updateSpellLuckCount(token: TokenEntity, eventId: String) {
        val spellLuckArr = JSONArray(token.spellLuckCount)
        val lastHelpTimeStr = spellLuckArr.getString(0)
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val today = sdf.format(System.currentTimeMillis())

        if (lastHelpTimeStr!= today) {
            val pointExchangeData = spellLuckArr.getJSONArray(2)
            spellLuckArr.put(0, today) // 更新日期
            spellLuckArr.put(1, spellLuckArr.getInt(1) + 1) // 增加计数
            spellLuckArr.put(2, pointExchangeData.put(eventId)) // 添加eventId记录
        }
        
        token.spellLuckCount = spellLuckArr.toString()
    }
} 