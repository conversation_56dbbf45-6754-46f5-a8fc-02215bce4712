package dev.pigmomo.yhhelper2025.ui.viewmodel

import android.annotation.SuppressLint
import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import dev.pigmomo.yhhelper2025.data.AppDatabase
import dev.pigmomo.yhhelper2025.data.repository.TokenRepository

/**
 * ViewModelFactory - 用于创建ViewModel实例
 */
class ViewModelFactory private constructor(private val context: Context) : ViewModelProvider.Factory {
    
    // 缓存TokenRepository实例
    private var tokenRepositoryInstance: TokenRepository? = null
    
    /**
     * 获取TokenRepository实例，确保数据库连接正常
     */
    private fun getTokenRepository(): TokenRepository {
        try {
            // 检查数据库是否已关闭，如果已关闭则重新创建TokenRepository
            val db = AppDatabase.getInstance(context)
            if (tokenRepositoryInstance == null) {
                tokenRepositoryInstance = TokenRepository(db.tokenDao())
                // 设置数据库重连回调
                tokenRepositoryInstance?.setDatabaseReopenCallback {
                    val newDb = AppDatabase.getInstance(context)
                    newDb.tokenDao()
                }
            }
            return tokenRepositoryInstance!!
        } catch (e: Exception) {
            // 如果发生异常，重新创建TokenRepository
            val db = AppDatabase.getInstance(context)
            tokenRepositoryInstance = TokenRepository(db.tokenDao())
            // 设置数据库重连回调
            tokenRepositoryInstance?.setDatabaseReopenCallback {
                val newDb = AppDatabase.getInstance(context)
                newDb.tokenDao()
            }
            return tokenRepositoryInstance!!
        }
    }
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        val tokenRepository = getTokenRepository()
        
        return when {
            modelClass.isAssignableFrom(MainViewModel::class.java) -> {
                MainViewModel(tokenRepository) as T
            }
            modelClass.isAssignableFrom(HelpCouponViewModel::class.java) -> {
                HelpCouponViewModel(tokenRepository) as T
            }
            modelClass.isAssignableFrom(PointExchangeViewModel::class.java) -> {
                PointExchangeViewModel(tokenRepository) as T
            }
            modelClass.isAssignableFrom(SpellLuckViewModel::class.java) -> {
                SpellLuckViewModel(tokenRepository) as T
            }
            modelClass.isAssignableFrom(TokenInfoViewModel::class.java) -> {
                TokenInfoViewModel(tokenRepository) as T
            }
            modelClass.isAssignableFrom(SettingViewModel::class.java) -> {
                SettingViewModel(context.applicationContext) as T
            }
            else -> throw IllegalArgumentException("未知的ViewModel类: ${modelClass.name}")
        }
    }
    
    companion object {
        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var instance: ViewModelFactory? = null
        
        fun getInstance(context: Context): ViewModelFactory {
            return instance ?: synchronized(ViewModelFactory::class) {
                instance ?: ViewModelFactory(context.applicationContext).also { instance = it }
            }
        }
        
        /**
         * 重置工厂实例，在数据库操作后调用以确保获取新的数据库连接
         */
        fun resetInstance() {
            synchronized(ViewModelFactory::class) {
                instance?.tokenRepositoryInstance = null
            }
        }
    }
}