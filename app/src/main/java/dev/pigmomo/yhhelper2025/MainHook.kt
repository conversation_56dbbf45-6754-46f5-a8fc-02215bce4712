package dev.pigmomo.yhhelper2025

import android.content.Context
import de.robv.android.xposed.IXposedHookLoadPackage
import de.robv.android.xposed.XC_MethodHook
import de.robv.android.xposed.callbacks.XC_LoadPackage
import de.robv.android.xposed.XposedHelpers
import de.robv.android.xposed.XposedBridge
import dev.pigmomo.yhhelper2025.utils.MessageUtils

private const val WECHAT_PACKAGE = "com.tencent.mm"

class MainHook : IXposedHookLoadPackage {
    private val messageUtils = MessageUtils()
    
    override fun handleLoadPackage(lpparam: XC_LoadPackage.LoadPackageParam) {
        try {
            if (lpparam.packageName == WECHAT_PACKAGE) {
                XposedBridge.log("yhhelper2025: start! Package: ${lpparam.packageName}, Process: ${lpparam.processName}")
                
                weChatHook(lpparam.classLoader)
                
                // 初始化Context
                initializeContext(lpparam.classLoader)
            }
        } catch (e: Throwable) {
            XposedBridge.log("yhhelper2025: hook error: ${e.message}")
            e.printStackTrace()
        }
    }
    
    /**
     * 初始化Context
     * @param classLoader 微信应用的ClassLoader
     */
    private fun initializeContext(classLoader: ClassLoader?) {
        try {
            // 尝试通过Application获取Context
            val applicationClass = XposedHelpers.findClass("android.app.Application", classLoader)
            XposedHelpers.findAndHookMethod(
                applicationClass,
                "attach",
                Context::class.java,
                object : XC_MethodHook() {
                    override fun afterHookedMethod(param: MethodHookParam) {
                        val context = param.args[0] as? Context
                        if (context != null) {
                            XposedBridge.log("yhhelper2025: got context from Application.attach")
                            messageUtils.initContext(context)
                        }
                    }
                }
            )
        } catch (e: Throwable) {
            XposedBridge.log("yhhelper2025: failed to hook Application.attach: ${e.message}")
        }
    }

    private fun weChatHook(classLoader: ClassLoader?) {
        try {
            // 查找SQLiteDatabase类
            val sqLiteDatabase =
                XposedHelpers.findClass("com.tencent.wcdb.database.SQLiteDatabase", classLoader)
            XposedBridge.log("yhhelper2025: find SQLiteDatabase")

            sqLiteDatabase.apply {
                XposedBridge.log("yhhelper2025: SQLiteDatabase.insertWithOnConflict start call")
                // insertWithOnConflict(String,String,ContentValues,int)
                XposedBridge.hookAllMethods(this, "insertWithOnConflict", object : XC_MethodHook() {
                    override fun beforeHookedMethod(param: MethodHookParam) {
                        //XposedBridge.log("yhhelper2025: SQLiteDatabase.insertWithOnConflict has called")
                        messageUtils.messageDeal(param)
                    }
                })
            }

            XposedBridge.log("yhhelper2025: success hook SQLiteDatabase.insertWithOnConflict")
        } catch (e: Throwable) {
            // 记录Hook异常
            XposedBridge.log("yhhelper2025: hook error: ${e.message}")
            e.printStackTrace()
        }
    }
}