package dev.pigmomo.yhhelper2025

import android.content.Intent
import dev.pigmomo.yhhelper2025.utils.NotificationUtils
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.BottomAppBarDefaults
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import dev.pigmomo.yhhelper2025.ui.HelpCouponActivity
import dev.pigmomo.yhhelper2025.ui.PointExchangeActivity
import dev.pigmomo.yhhelper2025.ui.SettingActivity
import dev.pigmomo.yhhelper2025.ui.SpellLuckActivity
import dev.pigmomo.yhhelper2025.ui.TokenInfoActivity
import dev.pigmomo.yhhelper2025.ui.components.DeleteConfirmDialog
import dev.pigmomo.yhhelper2025.ui.components.NoteEditDialog
import dev.pigmomo.yhhelper2025.ui.components.ResetConfirmDialog
import dev.pigmomo.yhhelper2025.ui.components.SearchBar
import dev.pigmomo.yhhelper2025.ui.components.TokenActionDialog
import dev.pigmomo.yhhelper2025.ui.components.TokenInsertDialog
import dev.pigmomo.yhhelper2025.ui.components.TokenItem
import dev.pigmomo.yhhelper2025.ui.theme.Yhhelper2025Theme
import dev.pigmomo.yhhelper2025.ui.viewmodel.MainViewModel
import dev.pigmomo.yhhelper2025.ui.viewmodel.ViewModelFactory
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.max
import kotlin.math.roundToInt

/**
 * 主活动 - 应用程序的入口点
 */
class MainActivity : ComponentActivity() {
    private lateinit var viewModel: MainViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化ViewModel
        viewModel =
            ViewModelProvider(this, ViewModelFactory.getInstance(this))[MainViewModel::class.java]

        // 创建通知渠道和显示通知
        NotificationUtils.createNotificationChannel(this)
        NotificationUtils.showNotification(this)

        setContent {
            Yhhelper2025Theme(dynamicColor = false) {
                MainScreen(
                    viewModel = viewModel
                )
            }
        }
    }

    override fun onResume() {
        super.onResume()

        // 重新获取ViewModel实例以确保TokenRepository引用的是有效的数据库连接
        // 先重置ViewModelFactory中缓存的TokenRepository实例
        ViewModelFactory.resetInstance()
        viewModel =
            ViewModelProvider(this, ViewModelFactory.getInstance(this))[MainViewModel::class.java]

        // 刷新令牌列表数据
        viewModel.loadAllTokens()

        // 更新链接数据并检查未使用的链接
        lifecycleScope.launch {
            viewModel.fetchLinksFromLocalServer()
            delay(500) // 等待数据加载

            // 检查是否有未使用的链接，如果有则显示对话框
            if (viewModel.links.value.any { !it.isUsed }) {
                viewModel.showLinksDialog = true
            }
        }

        // 检查剪贴板变化
        lifecycleScope.launch {
            delay(500)
            // 在Composable函数外不能使用LocalClipboardManager，需要在MainScreen中处理
            // 设置标志让MainScreen检查剪贴板
            viewModel.checkClipboardOnResume = true
        }
    }
}

/**
 * 主屏幕组件 - 应用程序的主界面
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun MainScreen(
    viewModel: MainViewModel
) {
    val context = LocalContext.current
    val clipboardManager = LocalClipboardManager.current
    val coroutineScope = rememberCoroutineScope()

    // 从ViewModel获取状态
    val tokens by viewModel.tokens.collectAsState()

    // 监听应用恢复时检查剪贴板
    LaunchedEffect(viewModel.checkClipboardOnResume) {
        if (viewModel.checkClipboardOnResume) {
            viewModel.handleClipboardTextChange(clipboardManager)
            viewModel.checkClipboardOnResume = false
        }
    }

    val itemHeight = 86
    val itemHeightInScreen = with(LocalDensity.current) { itemHeight.dp.toPx() }.roundToInt()
    val listScrollState = rememberLazyListState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Row {
                        Text("永助", maxLines = 1)
                        Text(
                            viewModel.searchTips,
                            fontSize = 10.sp,
                            maxLines = 1,
                            modifier = Modifier
                                .padding(start = 5.dp)
                                .offset(y = (5).dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    actionIconContentColor = Color.White
                ),
                modifier = Modifier.combinedClickable(
                    onClick = {},
                    onDoubleClick = {
                        coroutineScope.launch {
                            listScrollState.scrollToItem(0)
                        }
                    }
                ),
                actions = {
                    IconButton(onClick = {
                        viewModel.isSearchVisible = !viewModel.isSearchVisible
                        if (!viewModel.isSearchVisible) {
                            viewModel.clearSearchResults()
                        } else {
                            viewModel.searchTips = "请输入关键词！"
                        }
                    }) {
                        Icon(
                            imageVector = if (!viewModel.isSearchVisible) Icons.Filled.Search else Icons.Filled.Close,
                            contentDescription = "搜索",
                            tint = Color.White
                        )
                    }
                }
            )
        },
        bottomBar = {
            BottomAppBar(
                modifier = Modifier.combinedClickable(
                    onClick = {},
                    onDoubleClick = {
                        coroutineScope.launch {
                            if (tokens.isNotEmpty()) {
                                listScrollState.scrollToItem(tokens.size - 1)
                            }
                        }
                    }
                ),
                actions = {
                    IconButton(onClick = { viewModel.bottomAppBarMenuExpanded = true }) {
                        Icon(
                            Icons.Filled.Menu,
                            contentDescription = "菜单",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                    IconButton(onClick = {
                        // 获取所有链接
                        viewModel.showLinksDialog = true
                        // 从本地服务获取链接数据
                        viewModel.fetchLinksFromLocalServer()
                    }) {
                        Icon(
                            Icons.Filled.DateRange,
                            contentDescription = "记录",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                    IconButton(onClick = {
                        val intent = Intent(context, SettingActivity::class.java)
                        context.startActivity(intent)
                    }) {
                        Icon(
                            Icons.Filled.Settings,
                            contentDescription = "设置",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                },
                floatingActionButton = {
                    FloatingActionButton(
                        onClick = { viewModel.insertTokenDialog = true },
                        containerColor = BottomAppBarDefaults.bottomAppBarFabColor,
                        elevation = FloatingActionButtonDefaults.bottomAppBarFabElevation(),
                    ) {
                        Icon(Icons.Filled.Add, "添加")
                    }
                }
            )

            DropdownMenu(
                expanded = viewModel.bottomAppBarMenuExpanded,
                onDismissRequest = { viewModel.bottomAppBarMenuExpanded = false },
                modifier = Modifier.width(IntrinsicSize.Min)
            ) {
                DropdownMenuItem(text = {
                    Text("组队瓜分")
                }, onClick = {
                    viewModel.bottomAppBarMenuExpanded = false
                    val intent = Intent(context, PointExchangeActivity::class.java)
                    context.startActivity(intent)
                }, leadingIcon = {
                    Icon(
                        ImageVector.vectorResource(R.drawable.ic_friends),
                        contentDescription = null,
                        modifier = Modifier.size(width = 24.dp, height = 24.dp)
                    )
                })
                DropdownMenuItem(text = {
                    Text(" 拼手气")
                }, onClick = {
                    viewModel.bottomAppBarMenuExpanded = false
                    val intent = Intent(context, SpellLuckActivity::class.java)
                    context.startActivity(intent)
                }, leadingIcon = {
                    Icon(
                        ImageVector.vectorResource(R.drawable.baseline_call_split_24),
                        contentDescription = null,
                        modifier = Modifier.size(width = 24.dp, height = 24.dp)
                    )
                })
                DropdownMenuItem(text = {
                    Text(" 助力券")
                }, onClick = {
                    viewModel.bottomAppBarMenuExpanded = false
                    val intent = Intent(context, HelpCouponActivity::class.java)
                    context.startActivity(intent)
                }, leadingIcon = {
                    Icon(
                        ImageVector.vectorResource(R.drawable.ic_coupon),
                        contentDescription = null,
                        modifier = Modifier.size(width = 22.dp, height = 22.dp)
                    )
                })
            }
        },
    ) { innerPadding ->

        val innerPaddingFix = PaddingValues(
            top = innerPadding.calculateTopPadding() + 10.dp,
            bottom = innerPadding.calculateBottomPadding() + 10.dp
        )

        // 令牌列表
        LazyColumn(
            state = listScrollState,
            contentPadding = innerPaddingFix,
            verticalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            items(count = tokens.size) { index ->
                val token = tokens[index]

                TokenItem(
                    token = token,
                    index = index,
                    totalCount = tokens.size,
                    isSelected = viewModel.currentClickIndex == index,
                    isHighlighted = index == viewModel.searchTargetIndex && viewModel.filteredTokens.isNotEmpty(),
                    onClick = {
                        viewModel.currentClickIndex = index
                        val intent = Intent(context, TokenInfoActivity::class.java).apply {
                            putExtra("token", token.toString())
                        }
                        context.startActivity(intent)
                    },
                    onLongClick = {
                        viewModel.currentClickIndex = index
                        viewModel.isActionDialogVisible = true
                    },
                    itemHeight = itemHeight
                )
            }
        }

        // 搜索栏
        SearchBar(
            visible = viewModel.isSearchVisible,
            tokens = tokens,
            onSearchResult = { filtered, targetIndex, tips ->
                viewModel.filteredTokens = filtered
                
                if (viewModel.isSearchVisible) {
                    viewModel.searchTips = tips
                    viewModel.searchTargetIndex = targetIndex

                    // 滚动到目标位置
                    if (targetIndex >= 0) {
                        // 检查是否是保持当前选中项目的情况
                        val isKeepingCurrentItem = filtered.isNotEmpty() && 
                                viewModel.previousTargetIndex == targetIndex
                        
                        // 只有在不是保持当前选中项目的情况下才滚动
                        if (!isKeepingCurrentItem) {
                            coroutineScope.launch {
                                // 确保索引不小于0
                                val scrollIndex = maxOf(0, targetIndex - 1)
                                listScrollState.scrollToItem(scrollIndex)
                            }
                        }
                        
                        // 更新上一次的目标索引
                        viewModel.previousTargetIndex = targetIndex
                    }
                }
            },
            topPadding = innerPaddingFix.calculateTopPadding().value
        )
    }

    // 令牌插入对话框
    if (viewModel.insertTokenDialog) {
        TokenInsertDialog(
            onDismiss = { viewModel.insertTokenDialog = false },
            coroutineScope = coroutineScope,
            tokenRepository = viewModel.tokenRepository,
            tokensUpdate = { viewModel.loadAllTokens() },
            initialTokenText = if (viewModel.clipboardTextType == "INPUT TOKEN") viewModel.clipboardText else "",
            onScrollToToken = { token ->
                coroutineScope.launch {
                    delay(300)
                    viewModel.insertTokenDialog = false
                    val index = tokens.indexOfFirst { it.uid == token.uid }
                    viewModel.currentClickIndex = index
                    listScrollState.scrollToItem(max(index, 0))
                }
            }
        )
    }

    // 令牌操作对话框
    if (viewModel.isActionDialogVisible && viewModel.currentClickIndex >= 0 && viewModel.currentClickIndex < tokens.size) {
        TokenActionDialog(
            token = tokens[viewModel.currentClickIndex],
            onDismiss = { viewModel.isActionDialogVisible = false },
            onNoteClick = { viewModel.isNoteDialogVisible = true },
            onCopyClick = {
                viewModel.copyToken(
                    clipboardManager,
                    tokens[viewModel.currentClickIndex]
                )
            },
            onMarkOldClick = {
                viewModel.markTokenAsOld(viewModel.currentClickIndex)
                viewModel.isActionDialogVisible = false
            },
            onMarkLimitedClick = {
                viewModel.markTokenAsLimited(viewModel.currentClickIndex)
                viewModel.isActionDialogVisible = false
            },
            onDeleteClick = { viewModel.isDeleteDialogVisible = true },
            onResetClick = { viewModel.isResetDialogVisible = true }
        )
    }

    // 备注编辑对话框
    if (viewModel.isNoteDialogVisible && viewModel.currentClickIndex >= 0 && viewModel.currentClickIndex < tokens.size) {
        NoteEditDialog(
            initialNote = tokens[viewModel.currentClickIndex].extraNote,
            onDismiss = { viewModel.isNoteDialogVisible = false },
            onConfirm = { newNote ->
                viewModel.updateTokenNote(viewModel.currentClickIndex, newNote)
                viewModel.isNoteDialogVisible = false
            },
            clipboardManager = clipboardManager
        )
    }

    // 删除确认对话框
    if (viewModel.isDeleteDialogVisible && viewModel.currentClickIndex >= 0 && viewModel.currentClickIndex < tokens.size) {
        DeleteConfirmDialog(
            onDismiss = { viewModel.isDeleteDialogVisible = false },
            onConfirm = {
                val tokenToDelete = tokens[viewModel.currentClickIndex]
                viewModel.copyToken(clipboardManager, tokenToDelete)
                viewModel.deleteToken(tokenToDelete)
                viewModel.isDeleteDialogVisible = false
            }
        )
    }

    // 重置确认对话框
    if (viewModel.isResetDialogVisible && viewModel.currentClickIndex >= 0 && viewModel.currentClickIndex < tokens.size) {
        ResetConfirmDialog(
            onDismiss = { viewModel.isResetDialogVisible = false },
            onConfirm = {
                viewModel.resetToken(viewModel.currentClickIndex)
                viewModel.isResetDialogVisible = false
            }
        )
    }

    // 剪贴板内容对话框
    if (viewModel.showClipboardText && viewModel.clipboardText.isNotEmpty()) {
        ClipboardTextDialog(
            viewModel = viewModel,
            context = context
        )
    }

    // 链接数据对话框
    if (viewModel.showLinksDialog) {
        LinksDataDialog(
            viewModel = viewModel,
            context = context
        )
    }
}

/**
 * 剪贴板内容对话框
 */
@Composable
fun ClipboardTextDialog(
    viewModel: MainViewModel,
    context: android.content.Context
) {
    AlertDialog(
        onDismissRequest = { },
        containerColor = MaterialTheme.colorScheme.surfaceContainer,
        title = { Text(text = "最近复制") },
        text = {
            Text(
                text = viewModel.clipboardText,
                modifier = Modifier.padding(bottom = 10.dp)
            )
        },
        confirmButton = {
            Button(onClick = {
                when (viewModel.clipboardTextType) {
                    "组队瓜分" -> {
                        viewModel.showClipboardText = false
                        val intent = Intent(context, PointExchangeActivity::class.java)
                        context.startActivity(intent)
                    }

                    "拼手气" -> {
                        viewModel.showClipboardText = false
                        val intent = Intent(context, SpellLuckActivity::class.java)
                        context.startActivity(intent)
                    }

                    "助力券" -> {
                        viewModel.showClipboardText = false
                        val intent = Intent(context, HelpCouponActivity::class.java)
                        context.startActivity(intent)
                    }

                    "INPUT TOKEN" -> {
                        viewModel.showClipboardText = false
                        viewModel.insertTokenDialog = true
                    }

                    else -> {
                        viewModel.showClipboardText = false
                    }
                }
            }) {
                Text(viewModel.clipboardTextType.takeIf { it.isNotEmpty() } ?: "确认")
            }
        },
        dismissButton = {
            Button(onClick = { viewModel.showClipboardText = false }) {
                Text("取消")
            }
        }
    )
}

/**
 * 链接数据对话框
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun LinksDataDialog(
    viewModel: MainViewModel,
    context: android.content.Context
) {
    // 获取链接数据
    val links by viewModel.links.collectAsState()

    AlertDialog(
        onDismissRequest = {
            viewModel.saveLinksUsageStatus()
            viewModel.showLinksDialog = false
        },
        containerColor = MaterialTheme.colorScheme.surfaceContainer,
        title = {
            Column {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "助力参数"
                    )
                    if (viewModel.isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            strokeWidth = 2.dp
                        )
                    }
                }

                // 显示最后更新时间
                if (viewModel.lastUpdateTime.isNotEmpty()) {
                    Text(
                        text = "更新时间: ${viewModel.lastUpdateTime}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.7f)
                    )
                }
            }
        },
        text = {
            if (viewModel.loadError.isNotEmpty()) {
                // 显示错误信息
                Text(
                    text = viewModel.loadError,
                    color = MaterialTheme.colorScheme.error
                )
            } else if (links.isEmpty() && !viewModel.isLoading) {
                Text("暂无链接数据")
            } else {
                Column(modifier = Modifier.height(400.dp)) {
                    LazyColumn {
                        // 按日期分组链接
                        val groupedLinks = links.groupBy { it.getFormattedDate() }
                        
                        // 遍历每个日期组
                        groupedLinks.forEach { (date, linksInGroup) ->
                            // 添加日期分割线标题
                            stickyHeader {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .background(MaterialTheme.colorScheme.surface.copy(0.8f))
                                        .padding(vertical = 4.dp, horizontal = 8.dp),
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = date,
                                        style = MaterialTheme.typography.titleSmall,
                                        color = MaterialTheme.colorScheme.onPrimaryContainer
                                    )
                                    
                                    // 显示该日期下已使用/总数
                                    val usedInGroup = linksInGroup.count { it.isUsed }
                                    Text(
                                        text = "($usedInGroup/${linksInGroup.size})",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onPrimaryContainer
                                    )
                                }
                            }
                            
                            // 显示该日期下的所有链接
                            items(linksInGroup.size) { i ->
                                val link = linksInGroup[i]
                                Card(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(top = if (i == 0) 0.dp else 5.dp),
                                    colors = CardDefaults.cardColors(
                                        containerColor = when {
                                            link.isUsed -> MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                                            viewModel.recentClickedKeyParam == link.keyParam -> Color(0xFFFFCDD2) // 使用链接内容匹配
                                            else -> MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                                        }
                                    )
                                ) {
                                    Row(
                                        modifier = Modifier.combinedClickable(
                                            onClick = {
                                                if (!link.isUsed) {
                                                    // 记录被点击的链接内容
                                                    viewModel.recentClickedKeyParam = link.keyParam
                                                    
                                                    when (link.getLinkType()) {
                                                        "组队瓜分" -> {
                                                            val intent = Intent(
                                                                context,
                                                                PointExchangeActivity::class.java
                                                            )
                                                            intent.putExtra("keyParam", link.keyParam)
                                                            context.startActivity(intent)
                                                        }

                                                        "拼手气" -> {
                                                            val intent = Intent(
                                                                context,
                                                                SpellLuckActivity::class.java
                                                            )
                                                            intent.putExtra("keyParam", link.keyParam)
                                                            context.startActivity(intent)
                                                        }

                                                        "助力券" -> {
                                                            val intent = Intent(
                                                                context,
                                                                HelpCouponActivity::class.java
                                                            )
                                                            intent.putExtra("keyParam", link.keyParam)
                                                            context.startActivity(intent)
                                                        }

                                                        else -> {

                                                        }
                                                    }
                                                }
                                            },
                                            onLongClick = {
                                                // 找到原始链接在完整列表中的索引
                                                val originalIndex = links.indexOfFirst { it.keyParam == link.keyParam }
                                                if (originalIndex >= 0) {
                                                    viewModel.updateLinkUsageStatus(originalIndex, !link.isUsed)
                                                    viewModel.saveLinksUsageStatus()
                                                }
                                            }
                                        ),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Column(
                                            modifier = Modifier
                                                .weight(1f)
                                                .padding(8.dp)
                                        ) {
                                            Text(
                                                text = link.getFormattedTime(),
                                                style = MaterialTheme.typography.bodySmall,
                                                color = MaterialTheme.colorScheme.primary
                                            )

                                            val linkType = link.getLinkType()
                                            if (linkType.isNotEmpty()) {
                                                Text(
                                                    text = linkType,
                                                    style = MaterialTheme.typography.bodyMedium,
                                                    color = MaterialTheme.colorScheme.secondary
                                                )
                                            }

                                            Text(
                                                text = link.keyParam,
                                                style = MaterialTheme.typography.bodyMedium,
                                                maxLines = 1,
                                                modifier = Modifier.horizontalScroll(
                                                    rememberScrollState()
                                                )
                                            )
                                        }

                                        // 使用状态图标
                                        Icon(
                                            if (link.isUsed)
                                                Icons.Filled.CheckCircle
                                            else
                                                Icons.AutoMirrored.Filled.ArrowForward,
                                            contentDescription = "使用状态",
                                            tint = if (link.isUsed)
                                                MaterialTheme.colorScheme.primary
                                            else
                                                MaterialTheme.colorScheme.onSurfaceVariant.copy(
                                                    alpha = 0.6f
                                                ),
                                            modifier = Modifier
                                                .padding(end = 10.dp)
                                                .size(24.dp)
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            ) {
                // 刷新按钮
                OutlinedButton(onClick = { viewModel.fetchLinksFromLocalServer() }) {
                    Icon(
                        Icons.Filled.Refresh,
                        contentDescription = "刷新",
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("刷新")
                }

                // 关闭按钮
                Button(onClick = {
                    // 对话框关闭时保存使用状态
                    viewModel.saveLinksUsageStatus()
                    viewModel.showLinksDialog = false
                }) {
                    Text("关闭")
                }
            }
        },
        dismissButton = null
    )
}



