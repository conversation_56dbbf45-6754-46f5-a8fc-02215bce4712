<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <application
        android:allowBackup="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Yhhelper2025"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleInstancePerTask"
            android:theme="@style/Theme.Yhhelper2025">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.HelpCouponActivity"
            android:exported="false"
            android:theme="@style/Theme.Yhhelper2025"/>
        <activity
            android:name=".ui.TokenInfoActivity"
            android:exported="false"
            android:theme="@style/Theme.Yhhelper2025"/>
        <activity
            android:name=".ui.SettingActivity"
            android:exported="false"
            android:theme="@style/Theme.Yhhelper2025"/>
        <activity android:name=".ui.PointExchangeActivity"
            android:exported="false"
            android:theme="@style/Theme.Yhhelper2025"/>
        <activity
            android:name=".ui.SpellLuckActivity"
            android:exported="false"
            android:theme="@style/Theme.Yhhelper2025" />

        <!-- 状态栏贴片服务 -->
        <service
            android:name=".utils.AppTileService"
            android:exported="true"
            android:icon="@mipmap/ic_launcher"
            android:label="永助"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE">
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>
            <meta-data
                android:name="android.service.quicksettings.ACTIVE_TILE"
                android:value="false" />
        </service>

        <meta-data
            android:name="xposedmodule"
            android:value="true" />
        <meta-data
            android:name="xposeddescription"
            android:value="永辉生活套件" />
        <meta-data
            android:name="xposedminversion"
            android:value="89" />
        <meta-data
            android:name="xposedscope"
            android:resource="@array/xposedscope" />
    </application>

</manifest>