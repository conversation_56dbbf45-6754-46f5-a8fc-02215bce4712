# YHHelper 应用重构计划

## 1. 重构目标

- 优化代码结构，提高可维护性
- 改进UI/UX设计，提升用户体验
- 应用MVVM架构模式，分离关注点
- 增强代码可读性和可测试性
- 优化性能和资源使用

## 2. 当前代码分析

### 2.1 问题识别

- **代码组织问题**：
  - 业务逻辑和UI代码混合在一起，特别是在YHHelperSettingActivity中
  - 缺乏清晰的架构模式，导致代码耦合度高
  - 工具类和辅助函数分散，缺乏系统性组织

- **UI设计问题**：
  - 界面元素重复定义，缺乏组件化思想
  - 样式和主题定义不一致
  - 缺少响应式设计考虑

- **数据处理问题**：
  - 数据库操作直接在UI代码中执行
  - 缺乏数据仓库层，导致数据访问逻辑重复
  - 异步操作处理不统一

### 2.2 代码结构现状

当前应用结构：
```
- MainActivity.kt (主活动)
- data/ (数据相关)
  - AppDatabase.kt
  - TokenDao.kt
  - TokenEntitty.kt
- service/ (服务)
  - CheckMessageService.kt
- ui/ (用户界面)
  - 多个Activity和Composable函数
  - theme/ (主题)
    - Color.kt
    - Theme.kt
    - Type.kt
  - floatingBall/ (悬浮球)
    - FloatingBall.kt
- util/ (工具类)
  - 多个工具函数
- viewModel/ (视图模型)
  - TokenInfoListViewModel.kt
  - TokenInfoListCountViewModel.kt
```

## 3. 重构策略

### 3.1 架构重构

采用MVVM (Model-View-ViewModel) 架构模式：

- **Model层**：
  - 创建Repository类，封装数据访问逻辑
  - 优化数据模型，增加必要的数据类
  - 将数据库操作从UI代码中分离

- **ViewModel层**：
  - 为每个主要功能创建ViewModel
  - 处理业务逻辑和数据转换
  - 管理UI状态和事件

- **View层**：
  - 将UI代码重构为可复用的Composable函数
  - 实现UI状态提升，减少状态管理复杂性
  - 创建一致的UI组件库

### 3.2 代码组织重构

新的包结构：
```
- data/
  - model/ (数据模型)
  - repository/ (数据仓库)
  - local/ (本地数据源)
    - dao/ (数据访问对象)
    - database/ (数据库)
  - remote/ (远程数据源，如有)
- di/ (依赖注入)
- ui/
  - theme/ (主题)
  - components/ (可复用UI组件)
  - screens/ (各个屏幕)
    - settings/ (设置相关)
    - tokens/ (令牌相关)
    - webview/ (WebView相关)
  - navigation/ (导航)
- viewmodel/ (视图模型)
- util/ (工具类)
  - extensions/ (扩展函数)
- service/ (服务)
```

### 3.3 UI/UX改进

- 创建一致的UI组件库
- 优化用户流程和交互
- 改进错误处理和用户反馈
- 增强可访问性

### 3.4 性能优化

- 优化数据库操作
- 改进异步任务处理
- 减少不必要的UI重绘
- 优化资源使用

## 4. 重构步骤

### 4.1 准备工作

- 创建新的包结构
- 设置基础架构组件
- 创建核心数据模型和接口

### 4.2 数据层重构

- 创建TokenRepository
- 优化数据库访问
- 实现数据转换和映射函数

### 4.3 ViewModel层实现

- 创建SettingsViewModel
- 实现业务逻辑和数据处理
- 定义UI状态和事件

### 4.4 UI层重构

- 创建可复用的UI组件
- 重构设置屏幕
- 优化用户交互流程

### 4.5 测试和优化

- 编写单元测试
- 进行UI测试
- 性能分析和优化

## 5. 具体实现计划

### 5.1 YHHelperSettingActivity重构

1. 创建SettingsViewModel，将业务逻辑从Activity移至ViewModel
2. 将UI代码分解为可复用的Composable函数
3. 实现UI状态管理
4. 优化文件导入/导出功能

### 5.2 数据处理优化

1. 创建TokenRepository，封装数据访问逻辑
2. 优化数据库操作，使用协程和Flow
3. 实现数据缓存策略

### 5.3 UI组件库创建

1. 设计并实现通用按钮、对话框等组件
2. 创建一致的颜色和主题系统
3. 实现响应式布局

## 6. 预期成果

- 代码结构清晰，关注点分离
- UI组件可复用，设计一致
- 业务逻辑集中管理，易于维护
- 性能提升，用户体验改善
- 代码可测试性增强

## 7. 风险和缓解措施

- **风险**：重构可能引入新的bug
  - **缓解**：增加测试覆盖率，逐步重构并测试

- **风险**：重构工作量大，可能影响开发进度
  - **缓解**：分阶段实施，优先重构核心功能

- **风险**：团队成员可能需要适应新架构
  - **缓解**：提供文档和培训，确保知识共享